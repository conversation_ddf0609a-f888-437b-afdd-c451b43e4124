"""
Bronze Layer - Data Extraction
This module handles the extraction of raw data from SQLite databases and CSV files,
storing it in the bronze layer with minimal transformation.
"""
import os
import sqlite3
import pandas as pd
from pathlib import Path
from loguru import logger
import config

def extract_project_data(project_code):
    """
    Extract data from a project's SQLite database
    
    Args:
        project_code (str): Project code (e.g., BE01)
        
    Returns:
        tuple: (project_df, expenses_df) DataFrames containing project and expenses data
    """
    db_path = config.DATA_DIR / f"{project_code}.db"
    
    if not db_path.exists():
        logger.error(f"Database file not found: {db_path}")
        return None, None
    
    try:
        # Connect to SQLite database
        conn = sqlite3.connect(db_path)
        
        # Extract project data
        project_df = pd.read_sql("SELECT * FROM project", conn)
        
        # Extract expenses data
        expenses_df = pd.read_sql("SELECT * FROM expenses", conn)
        
        # Add project_code column to both dataframes for traceability
        project_df['source_file'] = f"{project_code}.db"
        expenses_df['project_code'] = project_code
        expenses_df['source_file'] = f"{project_code}.db"
        
        conn.close()
        
        logger.info(f"Extracted data from {project_code}.db: {len(project_df)} projects, {len(expenses_df)} expense records")
        return project_df, expenses_df
    
    except Exception as e:
        logger.error(f"Error extracting data from {project_code}.db: {str(e)}")
        return None, None

def extract_budget_data(project_code):
    """
    Extract budget data from a project's CSV file
    
    Args:
        project_code (str): Project code (e.g., BE01)
        
    Returns:
        DataFrame: Budget data
    """
    # Handle the special case for KE02 (file is named KEO2_budget.csv)
    if project_code == "KE02":
        csv_path = config.DATA_DIR / "KEO2_budget.csv"
    else:
        csv_path = config.DATA_DIR / f"{project_code}_budget.csv"
    
    if not csv_path.exists():
        logger.error(f"Budget CSV file not found: {csv_path}")
        return None
    
    try:
        # Read CSV file
        budget_df = pd.read_csv(csv_path)
        
        # Add project_code column for traceability
        budget_df['project_code'] = project_code
        budget_df['source_file'] = csv_path.name
        
        logger.info(f"Extracted budget data from {csv_path.name}: {len(budget_df)} records")
        return budget_df
    
    except Exception as e:
        logger.error(f"Error extracting budget data from {csv_path.name}: {str(e)}")
        return None

def process_all_projects():
    """
    Process all projects and save to bronze layer
    
    Returns:
        tuple: (all_projects_df, all_expenses_df, all_budgets_df) Combined DataFrames
    """
    all_projects = []
    all_expenses = []
    all_budgets = []
    
    for project_code in config.PROJECT_CODES:
        # Extract project and expenses data
        project_df, expenses_df = extract_project_data(project_code)
        if project_df is not None and expenses_df is not None:
            all_projects.append(project_df)
            all_expenses.append(expenses_df)
        
        # Extract budget data
        budget_df = extract_budget_data(project_code)
        if budget_df is not None:
            all_budgets.append(budget_df)
    
    # Combine all data
    if all_projects:
        all_projects_df = pd.concat(all_projects, ignore_index=True)
        all_projects_df.to_parquet(config.BRONZE_DIR / "projects.parquet")
        logger.info(f"Saved {len(all_projects_df)} project records to bronze layer")
    else:
        all_projects_df = pd.DataFrame()
        logger.warning("No project data extracted")
    
    if all_expenses:
        all_expenses_df = pd.concat(all_expenses, ignore_index=True)
        all_expenses_df.to_parquet(config.BRONZE_DIR / "expenses.parquet")
        logger.info(f"Saved {len(all_expenses_df)} expense records to bronze layer")
    else:
        all_expenses_df = pd.DataFrame()
        logger.warning("No expense data extracted")
    
    if all_budgets:
        all_budgets_df = pd.concat(all_budgets, ignore_index=True)
        all_budgets_df.to_parquet(config.BRONZE_DIR / "budgets.parquet")
        logger.info(f"Saved {len(all_budgets_df)} budget records to bronze layer")
    else:
        all_budgets_df = pd.DataFrame()
        logger.warning("No budget data extracted")
    
    return all_projects_df, all_expenses_df, all_budgets_df

if __name__ == "__main__":
    logger.info("Starting Bronze Layer - Data Extraction")
    process_all_projects()
    logger.info("Bronze Layer processing completed")
