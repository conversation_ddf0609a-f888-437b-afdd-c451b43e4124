"""
MSF Data Engineering Project - Main Execution Script
This script orchestrates the entire data pipeline from Bronze to Gold layers
"""
import sys
import os
from pathlib import Path
from loguru import logger
import pandas as pd
import config

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from bronze_layer import process_all_projects
from silver_layer import SilverLayerProcessor
from gold_layer import GoldLayerProcessor

def setup_logging():
    """Configure logging"""
    logger.remove()  # Remove default handler
    logger.add(
        sys.stdout,
        format=config.LOG_FORMAT,
        level=config.LOG_LEVEL,
        colorize=True
    )
    logger.add(
        config.OUTPUT_DIR / "pipeline.log",
        format=config.LOG_FORMAT,
        level=config.LOG_LEVEL,
        rotation="10 MB"
    )

def run_pipeline():
    """Run the complete data pipeline"""
    logger.info("=" * 60)
    logger.info("MSF Data Engineering Pipeline - Starting")
    logger.info("=" * 60)
    
    try:
        # Bronze Layer - Data Extraction
        logger.info("Step 1: Bronze Layer - Data Extraction")
        logger.info("-" * 40)
        projects_df, expenses_df, budgets_df = process_all_projects()
        
        if projects_df.empty and expenses_df.empty and budgets_df.empty:
            logger.error("No data extracted in Bronze layer. Stopping pipeline.")
            return False
        
        # Silver Layer - Data Transformation
        logger.info("Step 2: Silver Layer - Data Transformation")
        logger.info("-" * 40)
        silver_processor = SilverLayerProcessor()
        clean_projects, clean_expenses, clean_budgets = silver_processor.process_silver_layer()
        
        # Gold Layer - Data Modeling
        logger.info("Step 3: Gold Layer - Data Modeling")
        logger.info("-" * 40)
        gold_processor = GoldLayerProcessor()
        gold_tables = gold_processor.process_gold_layer()

        # Step 4: Generate CSV exports for inspection
        logger.info("Step 4: CSV Export Generation")
        logger.info("-" * 40)
        generate_csv_exports()

        # Step 5: Generate Architecture Diagram
        logger.info("Step 5: Architecture Diagram Generation")
        logger.info("-" * 40)
        generate_architecture_diagram()

        logger.info("=" * 60)
        logger.info("MSF Data Engineering Pipeline - Completed Successfully")
        logger.info("=" * 60)
        
        # Print summary statistics
        print_summary(projects_df, expenses_df, budgets_df, gold_tables)
        
        return True
        
    except Exception as e:
        logger.error(f"Pipeline failed with error: {str(e)}")
        logger.exception("Full error traceback:")
        return False

def print_summary(projects_df, expenses_df, budgets_df, gold_tables):
    """Print pipeline summary statistics"""
    logger.info("PIPELINE SUMMARY")
    logger.info("=" * 60)
    
    # Bronze layer summary
    logger.info("Bronze Layer:")
    logger.info(f"  - Projects: {len(projects_df)} records")
    logger.info(f"  - Expenses: {len(expenses_df)} records")
    logger.info(f"  - Budgets: {len(budgets_df)} records")
    
    # Gold layer summary
    if gold_tables:
        logger.info("Gold Layer:")
        for table_name, df in gold_tables.items():
            if not df.empty:
                logger.info(f"  - {table_name}: {len(df)} records")
    
    # Data quality checks
    logger.info("Data Quality:")
    if not expenses_df.empty:
        total_expenses_eur = gold_tables.get('fact_expenses', pd.DataFrame())
        if not total_expenses_eur.empty and 'amount_eur' in total_expenses_eur.columns:
            total_amount = total_expenses_eur['amount_eur'].sum()
            logger.info(f"  - Total expenses (EUR): {total_amount:,.2f}")
    
    if not budgets_df.empty:
        total_budgets_eur = gold_tables.get('fact_budgets', pd.DataFrame())
        if not total_budgets_eur.empty and 'budget_eur' in total_budgets_eur.columns:
            total_budget = total_budgets_eur['budget_eur'].sum()
            logger.info(f"  - Total budget (EUR): {total_budget:,.2f}")
    
    logger.info("=" * 60)

def generate_csv_exports():
    """Generate CSV exports from Parquet files"""
    try:
        # Create CSV export directory
        csv_export_dir = config.OUTPUT_DIR / "csv_exports"
        csv_export_dir.mkdir(exist_ok=True)

        # Create subdirectories
        for layer in ['bronze', 'silver', 'gold']:
            (csv_export_dir / layer).mkdir(exist_ok=True)

        # Convert each layer
        layers = {
            'bronze': config.BRONZE_DIR,
            'silver': config.SILVER_DIR,
            'gold': config.GOLD_DIR
        }

        summary_data = []

        for layer_name, layer_dir in layers.items():
            if layer_dir.exists():
                output_layer_dir = csv_export_dir / layer_name

                for parquet_file in layer_dir.glob("*.parquet"):
                    try:
                        # Read Parquet and convert to CSV
                        df = pd.read_parquet(parquet_file)
                        csv_filename = parquet_file.stem + ".csv"
                        csv_path = output_layer_dir / csv_filename
                        df.to_csv(csv_path, index=False)

                        # Add to summary
                        summary_data.append({
                            'layer': layer_name,
                            'filename': csv_filename,
                            'table_name': parquet_file.stem,
                            'rows': len(df),
                            'columns': len(df.columns),
                            'column_names': ', '.join(df.columns.tolist()[:5]) + ('...' if len(df.columns) > 5 else ''),
                            'file_size_kb': round(csv_path.stat().st_size / 1024, 1)
                        })

                        logger.info(f"Converted {parquet_file.name} -> {csv_filename} ({len(df)} rows)")

                    except Exception as e:
                        logger.error(f"Error converting {parquet_file}: {str(e)}")

        # Create data summary
        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_csv(csv_export_dir / "data_summary.csv", index=False)
            logger.info(f"Created data summary with {len(summary_data)} files")

        # Create sample queries file
        sample_queries = '''-- Sample SQL Queries for MSF Data Analysis

-- 1. Project Overview
SELECT project_name, country, region, project_currency
FROM dim_projects;

-- 2. Monthly Expense Trends
SELECT t.year_month, SUM(f.amount_eur) as total_expenses
FROM fact_expenses f
JOIN dim_time t ON f.time_key = t.time_key
GROUP BY t.year_month
ORDER BY t.year_month;

-- 3. Budget vs Actual by Project
SELECT p.project_name,
       SUM(b.budget_eur) as total_budget,
       SUM(b.amount_eur) as total_actual,
       SUM(b.variance_eur) as variance
FROM budget_vs_actual b
JOIN dim_projects p ON b.project_id = p.project_id
GROUP BY p.project_name
ORDER BY variance DESC;

-- 4. Top Expense Categories
SELECT c.department, c.category,
       COUNT(*) as transactions,
       SUM(f.amount_eur) as total_expenses
FROM fact_expenses f
JOIN dim_categories c ON f.category_key = c.category_id
GROUP BY c.department, c.category
ORDER BY total_expenses DESC;
'''

        with open(csv_export_dir / "sample_queries.sql", 'w') as f:
            f.write(sample_queries)

        logger.info(f"CSV exports completed: {csv_export_dir}")

    except Exception as e:
        logger.error(f"Error generating CSV exports: {str(e)}")

def generate_architecture_diagram():
    """Generate architecture diagram"""
    try:
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
        from matplotlib.patches import FancyBboxPatch

        fig, ax = plt.subplots(1, 1, figsize=(14, 10))

        # Define colors
        colors = {
            'source': '#e1f5fe',
            'bronze': '#fff3e0',
            'silver': '#fff3e0',
            'gold': '#e8f5e8',
            'output': '#f3e5f5',
            'analytics': '#fce4ec'
        }

        # Source layer
        ax.add_patch(FancyBboxPatch((0.5, 8), 2, 1, boxstyle="round,pad=0.1",
                                    facecolor=colors['source'], edgecolor='black', linewidth=1))
        ax.text(1.5, 8.5, 'SQLite DBs\n(BE01.db, etc.)', ha='center', va='center', fontsize=9, weight='bold')

        ax.add_patch(FancyBboxPatch((3, 8), 2, 1, boxstyle="round,pad=0.1",
                                    facecolor=colors['source'], edgecolor='black', linewidth=1))
        ax.text(4, 8.5, 'CSV Files\n(*_budget.csv)', ha='center', va='center', fontsize=9, weight='bold')

        # Bronze layer
        ax.add_patch(FancyBboxPatch((1.5, 6.5), 2.5, 1, boxstyle="round,pad=0.1",
                                    facecolor=colors['bronze'], edgecolor='black', linewidth=1))
        ax.text(2.75, 7, 'Bronze Layer\nRaw Data Extraction', ha='center', va='center', fontsize=10, weight='bold')

        # Silver layer
        ax.add_patch(FancyBboxPatch((1.5, 5), 2.5, 1, boxstyle="round,pad=0.1",
                                    facecolor=colors['silver'], edgecolor='black', linewidth=1))
        ax.text(2.75, 5.5, 'Silver Layer\nCleaning & Currency\nConversion', ha='center', va='center', fontsize=10, weight='bold')

        # Gold layer components
        ax.add_patch(FancyBboxPatch((0.5, 3), 1.8, 1, boxstyle="round,pad=0.1",
                                    facecolor=colors['gold'], edgecolor='black', linewidth=1))
        ax.text(1.4, 3.5, 'Dimension\nTables', ha='center', va='center', fontsize=9, weight='bold')

        ax.add_patch(FancyBboxPatch((2.5, 3), 1.8, 1, boxstyle="round,pad=0.1",
                                    facecolor=colors['gold'], edgecolor='black', linewidth=1))
        ax.text(3.4, 3.5, 'Fact\nTables', ha='center', va='center', fontsize=9, weight='bold')

        ax.add_patch(FancyBboxPatch((4.5, 3), 1.8, 1, boxstyle="round,pad=0.1",
                                    facecolor=colors['gold'], edgecolor='black', linewidth=1))
        ax.text(5.4, 3.5, 'Summary\nTables', ha='center', va='center', fontsize=9, weight='bold')

        # Data warehouse
        ax.add_patch(FancyBboxPatch((1.5, 1.5), 2.5, 1, boxstyle="round,pad=0.1",
                                    facecolor=colors['output'], edgecolor='black', linewidth=1))
        ax.text(2.75, 2, 'SQLite Data Warehouse\n& CSV Exports', ha='center', va='center', fontsize=10, weight='bold')

        # Analytics outputs
        ax.add_patch(FancyBboxPatch((0.5, 0), 1.8, 1, boxstyle="round,pad=0.1",
                                    facecolor=colors['analytics'], edgecolor='black', linewidth=1))
        ax.text(1.4, 0.5, 'Power BI\nDashboards', ha='center', va='center', fontsize=9, weight='bold')

        ax.add_patch(FancyBboxPatch((2.5, 0), 1.8, 1, boxstyle="round,pad=0.1",
                                    facecolor=colors['analytics'], edgecolor='black', linewidth=1))
        ax.text(3.4, 0.5, 'Analysis\n& Reports', ha='center', va='center', fontsize=9, weight='bold')

        # Add arrows
        arrows = [
            ((1.5, 8.2), (2.5, 7.3)),  # SQLite to Bronze
            ((4, 8.2), (3, 7.3)),      # CSV to Bronze
            ((2.75, 6.5), (2.75, 6)),  # Bronze to Silver
            ((2.75, 5), (2.75, 4.2)),  # Silver to Gold
            ((2.75, 3), (2.75, 2.5)),  # Gold to Warehouse
            ((2.2, 1.5), (1.8, 1)),    # Warehouse to Power BI
            ((3.3, 1.5), (3.8, 1)),    # Warehouse to Analysis
        ]

        for start, end in arrows:
            ax.annotate('', xy=end, xytext=start,
                       arrowprops=dict(arrowstyle='->', lw=1.5, color='#333333'))

        # Set limits and remove axes
        ax.set_xlim(0, 7)
        ax.set_ylim(-0.5, 9.5)
        ax.set_aspect('equal')
        ax.axis('off')

        # Add title
        plt.title('MSF Data Engineering Architecture\nMedallion Architecture Implementation',
                  fontsize=16, weight='bold', pad=20)

        plt.tight_layout()
        plt.savefig('msf_architecture_diagram.png', dpi=300, bbox_inches='tight')
        plt.close()

        logger.info("Architecture diagram saved as: msf_architecture_diagram.png")

    except Exception as e:
        logger.error(f"Error generating architecture diagram: {str(e)}")

def main():
    """Main function"""
    # Setup logging
    setup_logging()
    
    # Check if data directory exists
    if not config.DATA_DIR.exists():
        logger.error(f"Data directory not found: {config.DATA_DIR}")
        logger.error("Please ensure the OneDrive_1_7-4-2025 directory exists with the required data files")
        return
    
    # Check for required files
    required_files = []
    for project_code in config.PROJECT_CODES:
        required_files.append(f"{project_code}.db")
        if project_code == "KE02":
            required_files.append("KEO2_budget.csv")
        else:
            required_files.append(f"{project_code}_budget.csv")
    
    missing_files = []
    for file_name in required_files:
        if not (config.DATA_DIR / file_name).exists():
            missing_files.append(file_name)
    
    if missing_files:
        logger.error(f"Missing required files: {missing_files}")
        logger.error("Please ensure all required data files are present")
        return
    
    # Run the pipeline
    success = run_pipeline()
    
    if success:
        logger.info("Pipeline completed successfully!")
        logger.info(f"Output files saved to: {config.OUTPUT_DIR}")
        logger.info(f"Database created at: {config.DATABASE_URL}")
    else:
        logger.error("Pipeline failed. Check logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
