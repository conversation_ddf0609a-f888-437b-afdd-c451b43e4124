# Power BI Recreation Guide - MSF Budget vs Expense Analysis

Based on the detailed descriptions provided, here are the complete specifications to recreate the three Power BI reports.

## Data Connection Setup

### 1. Connect to Data Source
- **Primary Source**: SQLite database `msf_data_warehouse.db`
- **Alternative**: Import CSV files from `output/csv_exports/gold/`

### 2. Key Tables to Import
- `fact_expenses` - Expense transactions
- `fact_budgets` - Budget allocations  
- `dim_projects` - Project information
- `dim_time` - Time dimension
- `dim_categories` - Department/Category information
- `budget_vs_actual` - Pre-calculated variance analysis

### 3. Create Relationships
```
dim_projects[project_id] → fact_expenses[project_id]
dim_projects[project_id] → fact_budgets[project_id]
dim_time[time_key] → fact_expenses[time_key]
dim_time[time_key] → fact_budgets[time_key]
dim_categories[category_id] → fact_expenses[category_key]
dim_categories[category_id] → fact_budgets[category_key]
```

## DAX Measures

### Core Measures
```dax
// Total Budget
Total Budget = SUM(fact_budgets[budget_eur])

// Total Expense  
Total Expense = SUM(fact_expenses[amount_eur])

// Monthly Budget
Monthly Budget = SUM(fact_budgets[budget_eur])

// Monthly Expense
Monthly Expense = SUM(fact_expenses[amount_eur])

// Accumulated Budget
Accumulated Budget = 
CALCULATE(
    SUM(fact_budgets[budget_eur]),
    FILTER(
        ALL(dim_time),
        dim_time[time_key] <= MAX(dim_time[time_key])
    )
)

// Accumulated Expenses
Accumulated Expenses = 
CALCULATE(
    SUM(fact_expenses[amount_eur]),
    FILTER(
        ALL(dim_time),
        dim_time[time_key] <= MAX(dim_time[time_key])
    )
)

// Budget Variance
Budget Variance = [Total Budget] - [Total Expense]

// Variance Percentage
Variance % = 
DIVIDE([Budget Variance], [Total Budget], 0) * 100
```

### Time Intelligence Measures
```dax
// Month ID (for display)
Month ID = 
FORMAT(
    DATE(dim_time[year], dim_time[month], 1),
    "mmm yyyy"
)
```

---

# Report 1: Budget vs Expense Analysis (Belgium Overview)

## Layout Structure
- **Title**: "Budget vs Expense Analysis" (top left)
- **Filters**: Horizontal row at top
- **KPI Cards**: Two large cards below filters
- **Charts**: Two bar charts stacked vertically

## Slicers/Filters
1. **Date Slicer** (top center)
   - Type: Between slicer with slider
   - Field: `dim_time[year_month]`
   - Default: 1/01/2023 to 1/12/2025

2. **Department Filter** (top right)
   - Type: Dropdown slicer
   - Field: `dim_categories[department]`
   - Default: "All"

3. **Country Filter** (top far right)
   - Type: Dropdown slicer  
   - Field: `dim_projects[country]`
   - Default: "Belgium"

## Visualizations

### KPI Cards (Top Row)
1. **Total Budget Card**
   - Measure: `[Total Budget]`
   - Format: Currency (EUR)
   - Expected Value: ~235M EUR

2. **Total Expense Card**
   - Measure: `[Total Expense]`
   - Format: Currency (EUR)
   - Expected Value: ~224M EUR

### Bar Charts
1. **Monthly Budget Bar Chart**
   - Title: "Monthly Budget"
   - X-axis: `dim_time[year_month]` (formatted as "janv. 2023", etc.)
   - Y-axis: `[Monthly Budget]`
   - Color: Light blue
   - Data labels: On top of bars

2. **Monthly Expense Bar Chart**
   - Title: "Monthly Expense"
   - X-axis: `dim_time[year_month]`
   - Y-axis: `[Monthly Expense]`
   - Color: Dark blue
   - Data labels: On top of bars

## Formatting
- **Color Scheme**: Shades of blue
- **Background**: Light/white
- **Font**: Clear, readable
- **Borders**: Subtle separators between sections

---

# Report 2: Budget vs Expense Analysis (Burkina Faso - Logistics)

## Same Layout as Report 1, Different Filters

## Slicers/Filters (Modified)
1. **Date Slicer**: Same as Report 1
2. **Department Filter**: Set to "Logistics"
3. **Country Filter**: Set to "Burkina Faso"

## Expected Values
- **Total Budget**: ~60M EUR
- **Total Expense**: ~56M EUR
- Shows monthly breakdown for Logistics in Burkina Faso only

---

# Report 3: Cumulated Budget and Expense Analysis

## Layout Structure
- **Title**: "Cumulated Budget and Expense Analysis"
- **Filters**: Same horizontal row
- **Top Row**: 2 KPI cards + 1 bar chart + 1 area chart
- **Bottom Row**: 1 bar chart + 1 area chart

## Slicers/Filters
1. **Date Slicer**: Same as previous reports
2. **Department Filter**: Set to "Logistics"  
3. **Country Filter**: Set to "Burkina Faso"

## Visualizations

### KPI Cards (Top Left)
1. **Accumulated Budget Card**
   - Measure: `[Accumulated Budget]`
   - Expected Value: ~60M EUR

2. **Accumulated Expenses Card**
   - Measure: `[Accumulated Expenses]`
   - Expected Value: ~56M EUR

### Bar Charts
1. **Accumulated by Department** (Top Center)
   - Title: "Accumulated Expenses and Accumulated budget by department"
   - Type: Clustered bar chart
   - X-axis: `dim_categories[department]`
   - Y-axis: `[Accumulated Budget]` and `[Accumulated Expenses]`
   - Colors: Dark blue (budget), Light blue (expenses)

2. **Accumulated by Name** (Bottom Left)
   - Title: "Accumulated Expenses and Accumulated budget by name"
   - Type: Clustered bar chart
   - X-axis: `dim_projects[project_name]`
   - Y-axis: `[Accumulated Budget]` and `[Accumulated Expenses]`
   - Expected projects: "Koudougou HIV", "Ouagadougou Coordo"
   - Expected values:
     - Koudougou HIV: 31M budget, 30M expenses
     - Ouagadougou Coordo: 29M budget, 26M expenses

### Area Charts
1. **Accumulated Expenses Trend** (Top Right)
   - Title: "Accumulated Expenses"
   - Type: Area chart
   - X-axis: `dim_time[year_month]` (labeled as "key")
   - Y-axis: `[Accumulated Expenses]`
   - Color: Light blue

2. **Accumulated Expenses Trend 2** (Bottom Right)
   - Title: "Accumulated Expenses"
   - Type: Area chart
   - X-axis: `dim_time[year_month]`
   - Y-axis: `[Accumulated Expenses]` (possibly different context)
   - Color: Purple

## Color Scheme
- **Budget**: Dark blue
- **Expenses**: Light blue  
- **Alternative Expenses**: Purple
- **Background**: Light/white

---

# Implementation Steps

## Step 1: Data Preparation
1. Import data from SQLite database or CSV files
2. Create relationships between tables
3. Verify data types and formatting

## Step 2: Create Measures
1. Add all DAX measures listed above
2. Test measures with sample data
3. Format measures appropriately (currency, percentages)

## Step 3: Build Report 1
1. Add title and slicers
2. Create KPI cards with proper formatting
3. Add monthly bar charts
4. Apply Belgium country filter by default

## Step 4: Duplicate for Report 2
1. Copy Report 1 page
2. Change default filters to Burkina Faso + Logistics
3. Verify values match expected results

## Step 5: Build Report 3
1. Create new page with cumulative focus
2. Add accumulated measures and charts
3. Include project-level breakdown
4. Add area charts for trend analysis

## Step 6: Formatting and Polish
1. Apply consistent color scheme
2. Add proper titles and labels
3. Format numbers and currencies
4. Test all interactions and filters

This guide should allow you to recreate the exact Power BI reports shown in your screenshots!
