"""
Analysis Examples for MSF Data Engineering Project
This script demonstrates how to query and analyze the processed data
"""
import sqlite3
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

# Database connection
DB_PATH = "msf_data_warehouse.db"

def connect_to_db():
    """Create database connection"""
    return sqlite3.connect(DB_PATH)

def get_project_overview():
    """Get overview of all projects"""
    conn = connect_to_db()
    
    query = """
    SELECT 
        p.project_name,
        p.country,
        p.region,
        p.project_currency,
        COUNT(DISTINCT f.time_key) as months_active,
        SUM(f.amount_eur) as total_expenses_eur,
        AVG(f.amount_eur) as avg_monthly_expense_eur
    FROM dim_projects p
    LEFT JOIN fact_expenses f ON p.project_id = f.project_id
    GROUP BY p.project_id, p.project_name, p.country, p.region, p.project_currency
    ORDER BY total_expenses_eur DESC
    """
    
    df = pd.read_sql(query, conn)
    conn.close()
    
    print("PROJECT OVERVIEW")
    print("=" * 80)
    print(df.to_string(index=False))
    return df

def get_monthly_trends():
    """Get monthly expense trends across all projects"""
    conn = connect_to_db()
    
    query = """
    SELECT 
        t.year,
        t.month_name,
        t.year_month,
        SUM(f.amount_eur) as total_expenses_eur,
        COUNT(DISTINCT f.project_id) as active_projects
    FROM dim_time t
    LEFT JOIN fact_expenses f ON t.time_key = f.time_key
    WHERE f.amount_eur IS NOT NULL
    GROUP BY t.year, t.month, t.month_name, t.year_month
    ORDER BY t.year, t.month
    """
    
    df = pd.read_sql(query, conn)
    conn.close()
    
    print("\nMONTHLY TRENDS")
    print("=" * 80)
    print(df.to_string(index=False))
    return df

def get_category_analysis():
    """Analyze expenses by department and category"""
    conn = connect_to_db()
    
    query = """
    SELECT 
        c.department,
        c.category,
        COUNT(*) as transaction_count,
        SUM(f.amount_eur) as total_expenses_eur,
        AVG(f.amount_eur) as avg_expense_eur,
        MIN(f.amount_eur) as min_expense_eur,
        MAX(f.amount_eur) as max_expense_eur
    FROM dim_categories c
    JOIN fact_expenses f ON c.category_id = f.category_key
    GROUP BY c.department, c.category
    ORDER BY total_expenses_eur DESC
    """
    
    df = pd.read_sql(query, conn)
    conn.close()
    
    print("\nCATEGORY ANALYSIS")
    print("=" * 80)
    print(df.to_string(index=False))
    return df

def get_budget_variance_analysis():
    """Analyze budget vs actual variances"""
    conn = connect_to_db()
    
    query = """
    SELECT 
        p.project_name,
        c.department,
        t.year,
        SUM(b.budget_eur) as total_budget_eur,
        SUM(b.amount_eur) as total_actual_eur,
        SUM(b.variance_eur) as total_variance_eur,
        AVG(b.variance_pct) as avg_variance_pct
    FROM budget_vs_actual b
    JOIN dim_projects p ON b.project_id = p.project_id
    JOIN dim_categories c ON b.category_key = c.category_id
    JOIN dim_time t ON b.time_key = t.time_key
    GROUP BY p.project_name, c.department, t.year
    HAVING total_budget_eur > 0
    ORDER BY total_variance_eur DESC
    """
    
    df = pd.read_sql(query, conn)
    conn.close()
    
    print("\nBUDGET VARIANCE ANALYSIS")
    print("=" * 80)
    print(df.to_string(index=False))
    return df

def get_regional_comparison():
    """Compare expenses by region"""
    conn = connect_to_db()
    
    query = """
    SELECT 
        p.region,
        COUNT(DISTINCT p.project_id) as project_count,
        SUM(f.amount_eur) as total_expenses_eur,
        AVG(f.amount_eur) as avg_expense_eur,
        COUNT(*) as transaction_count
    FROM dim_projects p
    JOIN fact_expenses f ON p.project_id = f.project_id
    GROUP BY p.region
    ORDER BY total_expenses_eur DESC
    """
    
    df = pd.read_sql(query, conn)
    conn.close()
    
    print("\nREGIONAL COMPARISON")
    print("=" * 80)
    print(df.to_string(index=False))
    return df

def create_visualizations():
    """Create basic visualizations"""
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
        
        # Set style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # Create output directory for plots
        plots_dir = Path("output/plots")
        plots_dir.mkdir(exist_ok=True)
        
        # 1. Project expenses comparison
        project_df = get_project_overview()
        if not project_df.empty:
            plt.figure(figsize=(12, 6))
            plt.bar(project_df['project_name'], project_df['total_expenses_eur'])
            plt.title('Total Expenses by Project')
            plt.xlabel('Project')
            plt.ylabel('Total Expenses (EUR)')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig(plots_dir / 'expenses_by_project.png', dpi=300, bbox_inches='tight')
            plt.close()
        
        # 2. Monthly trends
        monthly_df = get_monthly_trends()
        if not monthly_df.empty:
            plt.figure(figsize=(12, 6))
            plt.plot(monthly_df['year_month'], monthly_df['total_expenses_eur'], marker='o')
            plt.title('Monthly Expense Trends')
            plt.xlabel('Month')
            plt.ylabel('Total Expenses (EUR)')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig(plots_dir / 'monthly_trends.png', dpi=300, bbox_inches='tight')
            plt.close()
        
        # 3. Category analysis
        category_df = get_category_analysis()
        if not category_df.empty:
            plt.figure(figsize=(10, 8))
            plt.pie(category_df['total_expenses_eur'], labels=category_df['category'], autopct='%1.1f%%')
            plt.title('Expenses by Category')
            plt.tight_layout()
            plt.savefig(plots_dir / 'expenses_by_category.png', dpi=300, bbox_inches='tight')
            plt.close()
        
        print(f"\nVisualizations saved to: {plots_dir}")
        
    except ImportError:
        print("\nMatplotlib/Seaborn not available. Skipping visualizations.")

def main():
    """Run all analyses"""
    print("MSF DATA ANALYSIS")
    print("=" * 80)
    
    # Check if database exists
    if not Path(DB_PATH).exists():
        print(f"Database not found: {DB_PATH}")
        print("Please run the main pipeline first: python main.py")
        return
    
    # Run analyses
    get_project_overview()
    get_monthly_trends()
    get_category_analysis()
    get_budget_variance_analysis()
    get_regional_comparison()
    
    # Create visualizations
    create_visualizations()
    
    print("\nAnalysis completed!")

if __name__ == "__main__":
    main()
