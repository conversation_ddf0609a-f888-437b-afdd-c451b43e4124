# Power BI Step-by-Step Guide - MSF Budget vs Expense Analysis

## 📋 **What You Need**

After running `python main.py`, you'll have these files ready for Power BI:

### 🎯 **Primary Option (Recommended): SQLite Database**
- **File**: `msf_data_warehouse.db` (in project root)
- **Why**: Single file, all relationships pre-configured, optimized for Power BI

### 📊 **Alternative Option: CSV Files**
- **Location**: `output/csv_exports/gold/` folder
- **Key Files**:
  - `fact_expenses.csv` - Main expense data (2,880 records)
  - `fact_budgets.csv` - Budget data (3,456 records)
  - `dim_projects.csv` - Project information (8 records)
  - `dim_time.csv` - Time dimension (36 records)
  - `dim_categories.csv` - Department/Category info (12 records)
  - `budget_vs_actual.csv` - Pre-calculated variance analysis (3,456 records)

---

## 🚀 **Method 1: SQLite Database (Recommended)**

### Step 1: Open Power BI Desktop
1. **Launch Power BI Desktop** (download from Microsoft if needed)
2. **Close** any welcome screens or templates

### Step 2: Connect to SQLite Database
1. **Click** "Get Data" button (Home ribbon)
   - **Shortcut**: `Ctrl + T`
2. **Search** for "SQLite" in the connector list
3. **Select** "SQLite database" → **Click** "Connect"
4. **Browse** to your project folder
5. **Select** `msf_data_warehouse.db` → **Click** "Open"

### Step 3: Select Tables
You'll see a **Navigator** window with all tables. **Check these boxes**:
- ✅ `dim_projects` (8 rows)
- ✅ `dim_time` (36 rows) 
- ✅ `dim_categories` (12 rows)
- ✅ `fact_expenses` (2,880 rows)
- ✅ `fact_budgets` (3,456 rows)
- ✅ `budget_vs_actual` (3,456 rows)

**Click** "Load" (not Transform)

### Step 4: Verify Data Model
1. **Click** "Model" view (left sidebar, 3rd icon)
   - **Shortcut**: `Ctrl + 3`
2. **Verify relationships** are automatically created:
   - `dim_projects[project_id]` ↔ `fact_expenses[project_id]`
   - `dim_time[time_key]` ↔ `fact_expenses[time_key]`
   - `dim_categories[category_id]` ↔ `fact_expenses[category_key]`
   - Similar relationships for `fact_budgets`

---

## 🎨 **Method 2: CSV Files (Alternative)**

### Step 1: Open Power BI Desktop
1. **Launch Power BI Desktop**
2. **Close** welcome screens

### Step 2: Import CSV Files
1. **Click** "Get Data" → **Text/CSV**
2. **Navigate** to `output/csv_exports/gold/`
3. **Select** `fact_expenses.csv` → **Click** "Open"
4. **Preview** data → **Click** "Load"
5. **Repeat** for each file:
   - `fact_budgets.csv`
   - `dim_projects.csv`
   - `dim_time.csv`
   - `dim_categories.csv`
   - `budget_vs_actual.csv`

### Step 3: Create Relationships (Manual)
1. **Go to** Model view (`Ctrl + 3`)
2. **Drag** from `dim_projects[project_id]` to `fact_expenses[project_id]`
3. **Drag** from `dim_time[time_key]` to `fact_expenses[time_key]`
4. **Drag** from `dim_categories[category_id]` to `fact_expenses[category_key]`
5. **Repeat** for `fact_budgets` table

---

## 📊 **Creating the Reports**

### Report 1: Budget vs Expense Analysis (Belgium)

#### Step 1: Create New Report Page
1. **Click** "Report" view (1st icon on left)
2. **Right-click** page tab → **Rename** to "Belgium Overview"

#### Step 2: Add Slicers (Filters)
1. **Drag** `dim_time[date]` to canvas
2. **Change** to **Slicer** (Visualizations pane)
3. **Format** as **Between** slicer (Format pane → General → Style)
4. **Position** at top center

5. **Drag** `dim_categories[department]` to canvas
6. **Change** to **Slicer** → **Dropdown** style
7. **Position** at top right

8. **Drag** `dim_projects[country]` to canvas
9. **Change** to **Slicer** → **Dropdown** style
10. **Position** at top far right
11. **Set default** to "Belgium" (Filter pane → Basic filtering → Select "Belgium")

#### Step 3: Add KPI Cards
1. **Insert** → **Card** visual
2. **Drag** `fact_budgets[budget_eur]` to **Fields**
3. **Title**: "Total Budget"
4. **Format** as Currency (Format pane → Data labels → Display units: Auto)

5. **Insert** another **Card** visual
6. **Drag** `fact_expenses[amount_eur]` to **Fields**
7. **Title**: "Total Expense"

#### Step 4: Add Monthly Charts
1. **Insert** → **Clustered Column Chart**
2. **X-axis**: `dim_time[month_id]`
3. **Y-axis**: `fact_budgets[budget_eur]`
4. **Title**: "Monthly Budget"
5. **Format** bars as light blue

6. **Insert** another **Clustered Column Chart**
7. **X-axis**: `dim_time[month_id]`
8. **Y-axis**: `fact_expenses[amount_eur]`
9. **Title**: "Monthly Expense"
10. **Format** bars as dark blue

---

### Report 2: Burkina Faso Logistics Analysis

#### Step 1: Duplicate Page
1. **Right-click** "Belgium Overview" tab
2. **Select** "Duplicate Page"
3. **Rename** to "Burkina Faso - Logistics"

#### Step 2: Change Filters
1. **Select** Country slicer
2. **Change** default to "Burkina Faso"
3. **Select** Department slicer
4. **Change** default to "Logistics"

---

### Report 3: Cumulative Analysis

#### Step 1: Create New Page
1. **Right-click** page tabs → **Insert Page**
2. **Rename** to "Cumulative Analysis"

#### Step 2: Add Accumulated Measures
**First, create DAX measures:**
1. **Click** "New Measure" (Home ribbon)
2. **Type**:
```dax
Accumulated Budget = 
CALCULATE(
    SUM(fact_budgets[budget_eur]),
    FILTER(
        ALL(dim_time[time_key]),
        dim_time[time_key] <= MAX(dim_time[time_key])
    )
)
```
3. **Press** Enter

4. **Create** another measure:
```dax
Accumulated Expenses = 
CALCULATE(
    SUM(fact_expenses[amount_eur]),
    FILTER(
        ALL(dim_time[time_key]),
        dim_time[time_key] <= MAX(dim_time[time_key])
    )
)
```

#### Step 3: Add Visuals
1. **Add** Card visuals for "Accumulated Budget" and "Accumulated Expenses"
2. **Add** Clustered Column Chart:
   - **X-axis**: `dim_categories[department]`
   - **Y-axis**: `[Accumulated Budget]` and `[Accumulated Expenses]`
3. **Add** Area Chart:
   - **X-axis**: `dim_time[month_id]`
   - **Y-axis**: `[Accumulated Expenses]`
4. **Add** Clustered Column Chart by project:
   - **X-axis**: `dim_projects[name]`
   - **Y-axis**: `[Accumulated Budget]` and `[Accumulated Expenses]`

---

## 🎨 **Formatting Tips**

### Colors
- **Budget**: Light blue (#5B9BD5)
- **Expenses**: Dark blue (#1F4E79)
- **Background**: White or light gray

### Fonts
- **Title**: Segoe UI, 16pt, Bold
- **Labels**: Segoe UI, 10pt
- **Values**: Segoe UI, 12pt, Bold

### Layout
- **Slicers**: Top row, evenly spaced
- **KPIs**: Large cards below slicers
- **Charts**: Stacked vertically below KPIs

---

## ⚡ **Power BI Shortcuts**

- **Get Data**: `Ctrl + T`
- **Refresh**: `F5`
- **New Measure**: `Alt + M`
- **Duplicate Page**: `Ctrl + D`
- **Copy Visual**: `Ctrl + C`
- **Paste Visual**: `Ctrl + V`
- **Report View**: `Ctrl + 1`
- **Data View**: `Ctrl + 2`
- **Model View**: `Ctrl + 3`

---

## 🔧 **Troubleshooting**

### If SQLite Connection Fails:
1. **Download** SQLite ODBC driver
2. **Use** CSV method instead
3. **Check** file path has no special characters

### If Data Looks Wrong:
1. **Check** relationships in Model view
2. **Verify** data types (numbers should be "Decimal Number")
3. **Refresh** data (`F5`)

### If Charts Are Empty:
1. **Check** filters are not too restrictive
2. **Verify** field assignments in Visualizations pane
3. **Check** data has values for selected time period

---

## 💾 **Saving Your Work**

1. **File** → **Save As**
2. **Name**: "MSF Budget vs Expense Analysis.pbix"
3. **Location**: Same folder as your data files

**Your Power BI file is now ready for sharing and publishing!**
