# MSF Data Engineering Project - Budget vs Expense Analysis

## Problem Statement

MSF (Médecins Sans Frontières) operates multiple humanitarian projects across different countries and needs a comprehensive data engineering solution to analyze budget vs expense performance. The organization has:

- **8 projects** across 4 countries (Belgium, Burkina Faso, Kenya, Senegal)
- **Expense data** stored in SQLite databases (one per project)
- **Budget data** stored in CSV files (one per project)
- **Multiple currencies** requiring conversion to EUR for analysis
- **Data quality issues** that need correction
- **Need for Power BI dashboards** for executive reporting

This solution implements a modern medallion architecture to process, clean, and transform the data for analytics and reporting.

## Architecture Overview

![MSF Data Engineering Architecture](msf_architecture_diagram.png)

The solution implements a **Medallion Architecture** with three layers:

### 🥉 Bronze Layer (Raw Data)
- Extracts data from 8 SQLite databases and CSV files
- Preserves original data structure and lineage
- Handles special cases (KEO2 vs KE02 filename mapping)

### 🥈 Silver Layer (Clean Data)
- Applies data quality corrections (geographical and currency fixes)
- Converts all currencies to EUR using historical exchange rates
- Standardizes data formats and validates business rules

### 🥇 Gold Layer (Analytics Ready)
- Creates star schema with fact and dimension tables
- Optimizes data for Power BI and reporting tools
- Generates summary tables and KPIs

## Project Structure

```
msf-augment/
├── OneDrive_1_7-4-2025/          # Source data files
│   ├── *.db                      # SQLite databases (BE01.db, BE55.db, etc.)
│   ├── *_budget.csv              # Budget CSV files
│   └── powerbi*.png              # Power BI report screenshots
├── src/                          # Source code modules
│   ├── bronze_layer.py           # Data extraction from raw sources
│   ├── silver_layer.py           # Data cleaning and transformation
│   ├── gold_layer.py             # Analytical data modeling
│   ├── exchange_rate_service.py  # Real currency conversion service
│   └── mock_exchange_service.py  # Mock service for testing
├── output/                       # Generated output files
│   ├── bronze/                   # Raw extracted data (Parquet)
│   ├── silver/                   # Cleaned data (Parquet)
│   ├── gold/                     # Analytics data (Parquet)
│   ├── csv_exports/              # CSV files for inspection
│   └── plots/                    # Generated visualizations
├── config.py                     # Configuration settings
├── main.py                       # Main pipeline execution
├── requirements.txt              # Python dependencies
├── msf_data_warehouse.db         # SQLite data warehouse
├── msf_architecture_diagram.png  # Architecture diagram
└── README.md                     # This documentation
```

## Features

### Medallion Architecture
- **Bronze Layer**: Raw data extraction from SQLite databases and CSV files
- **Silver Layer**: Data cleaning, standardization, and currency conversion
- **Gold Layer**: Analytical data modeling with fact and dimension tables

### Data Processing
- Extracts data from 8 MSF projects (BE01, BE55, BF01, BF02, KE01, KE02, SN01, SN02)
- Handles both expense data (from SQLite) and budget data (from CSV)
- Implements currency conversion using historical exchange rates
- Creates consistent data schema across all projects

### Currency Conversion
- Uses exchangerate-api.com for historical exchange rates
- Converts all amounts to EUR using end-of-month rates
- Handles multiple source currencies (USD, EUR, etc.)

### Data Quality
- Data validation and cleaning
- Missing data handling
- Consistent formatting and standardization
- Error logging and monitoring

## Quick Start

### 1. Prerequisites
- Python 3.8 or higher
- Internet connection (optional - for real exchange rate API)

### 2. Installation
```bash
# Navigate to project directory
cd msf-augment

# Install dependencies
pip install pandas numpy requests python-dotenv sqlalchemy loguru pyarrow matplotlib
```

### 3. Configuration (Optional)
```bash
# For production use with real exchange rates:
cp .env.template .env
# Edit .env and add your API key from https://www.exchangerate-api.com/

# For testing: The pipeline works with mock exchange rates by default
```

### 4. Run the Complete Pipeline
```bash
python main.py
```

**That's it!** The pipeline will automatically:
- ✅ Extract data from all SQLite databases and CSV files
- ✅ Clean and transform data with currency conversion
- ✅ Create analytical data models
- ✅ Generate CSV exports for inspection
- ✅ Create architecture diagram
- ✅ Build SQLite data warehouse for Power BI

## Output Files

After running `python main.py`, you'll get:

### 📊 **Data Warehouse**
- `msf_data_warehouse.db` - SQLite database ready for Power BI connection

### 📁 **CSV Exports** (in `output/csv_exports/`)
- `bronze/` - Raw extracted data
- `silver/` - Cleaned data with currency conversions
- `gold/` - Analytics-ready fact and dimension tables
- `data_summary.csv` - Overview of all datasets
- `sample_queries.sql` - SQL examples for analysis

### 📈 **Visualizations** (in `output/plots/`)
- `expenses_by_project.png`
- `monthly_trends.png`
- `expenses_by_category.png`

### 🏗️ **Architecture**
- `msf_architecture_diagram.png` - Visual architecture overview

## Data Model (Gold Layer)

### Fact Tables
- **`fact_expenses`** - Individual expense transactions (2,880 records)
- **`fact_budgets`** - Budget allocations (3,456 records)

### Dimension Tables
- **`dim_projects`** - Project information with regions (8 projects)
- **`dim_time`** - Time hierarchy (36 months: 2023-2025)
- **`dim_categories`** - Department/Category combinations (12 categories)

### Summary Tables
- **`budget_vs_actual`** - Variance analysis (3,456 records)
- **`monthly_expenses_by_project`** - Monthly aggregations (240 records)
- **`monthly_expenses_by_category`** - Category trends (360 records)

## Power BI Integration

### 🔌 **Connect to Data**
1. Open Power BI Desktop
2. Get Data → SQLite database
3. Select `msf_data_warehouse.db`
4. Import all tables

### 📊 **Ready-to-Use Tables**
All tables are optimized for Power BI with proper relationships and Power BI-friendly field names:
- `month_id` field formatted for display (e.g., "jan 2023")
- `name` field as alias for `project_name`
- `key` field for time-based charts

### 🎯 **Sample Reports Available**
The data supports recreation of three main report types:
1. **Budget vs Expense Analysis** - Overall performance with filters
2. **Department/Country Analysis** - Filtered views by logistics, countries
3. **Cumulative Analysis** - Accumulated trends and project breakdowns

See `PowerBI_Recreation_Guide.md` for detailed specifications and DAX measures.

## Key Features

### 🔧 **Data Quality Corrections**
- **Geographical Fix**: SN01 "Dakkar Coordo" corrected from Kenya → Senegal
- **Currency Fix**: BE01 project currency corrected from USD → EUR
- **File Mapping**: KEO2_budget.csv correctly mapped to KE02 project

### 💱 **Currency Conversion**
- All expenses converted to EUR using end-of-month historical rates
- Supports USD, EUR, XOF (West African CFA), KES (Kenyan Shilling)
- Mock service for testing, real API integration available

### 📈 **Professional Data Modeling**
- **Multiple Fact Tables**: `fact_expenses` and `fact_budgets` (industry standard)
- **Star Schema**: Optimized for analytical queries and Power BI
- **Time Intelligence**: Proper date hierarchies for trending analysis
- **Business KPIs**: Pre-calculated variance and summary metrics

## Data Processing Results

### 📊 **Volume Processed**
- **8 Projects** across 4 countries
- **3,456 Budget records** from CSV files
- **2,880 Expense records** (after data quality filtering)
- **36 Months** of data (Jan 2023 - Jun 2025)

### 💰 **Financial Summary**
- **Total Budget**: €10,411,489.91
- **Total Expenses**: €7,381,891.28
- **Variance**: €3,029,598.63 under budget (29%)

### 🌍 **Geographic Distribution**
- **Europe**: 2 projects (Belgium) - €2.03M expenses
- **Africa**: 6 projects (Burkina Faso, Kenya, Senegal) - €5.35M expenses

## Professional Data Warehousing

### ❓ **Why Multiple Fact Tables?**
In professional data warehousing, it's common to have multiple fact tables because:

1. **Different Grain**:
   - `fact_expenses` = Individual transactions (transactional grain)
   - `fact_budgets` = Budget allocations (planning grain)

2. **Different Sources**:
   - Expenses come from operational systems (SQLite DBs)
   - Budgets come from planning systems (CSV files)

3. **Different Update Cycles**:
   - Expenses updated daily/weekly
   - Budgets updated monthly/quarterly

4. **Performance**:
   - Separate tables optimize query performance
   - Different aggregation patterns for each business process

This follows **Kimball methodology** for dimensional modeling in enterprise data warehouses.

## Quick Analysis Examples

```bash
# View project overview
sqlite3 msf_data_warehouse.db "SELECT * FROM dim_projects;"

# Check monthly trends
sqlite3 msf_data_warehouse.db "SELECT year_month, SUM(amount_eur) FROM fact_expenses f JOIN dim_time t ON f.time_key = t.time_key GROUP BY year_month ORDER BY year_month;"

# Budget variance by project
sqlite3 msf_data_warehouse.db "SELECT project_id, SUM(variance_eur) FROM budget_vs_actual GROUP BY project_id ORDER BY SUM(variance_eur) DESC;"
```

## Support & Troubleshooting

- **Logs**: Check `output/pipeline.log` for detailed processing information
- **Data Validation**: Review `output/csv_exports/data_summary.csv` for data overview
- **Sample Queries**: Use `output/csv_exports/sample_queries.sql` for analysis examples
