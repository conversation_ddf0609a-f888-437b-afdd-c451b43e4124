"""
Exchange Rate Service
Handles currency conversion using the exchangerate-api.com service
"""
import requests
import pandas as pd
from datetime import datetime, date
from loguru import logger
import config

class ExchangeRateService:
    """Service for fetching and managing exchange rates"""
    
    def __init__(self, api_key=None):
        self.api_key = api_key or config.EXCHANGE_RATE_API_KEY
        self.base_url = config.EXCHANGE_RATE_BASE_URL
        self.cache = {}
    
    def get_last_day_of_month(self, year, month):
        """Get the last day of a given month"""
        if month == 12:
            next_month = date(year + 1, 1, 1)
        else:
            next_month = date(year, month + 1, 1)
        last_day = next_month - pd.Timedelta(days=1)
        return last_day.strftime('%Y-%m-%d')
    
    def fetch_historical_rates(self, base_currency, target_date):
        """
        Fetch historical exchange rates for a specific date
        
        Args:
            base_currency (str): Base currency code (e.g., 'USD')
            target_date (str): Date in YYYY-MM-DD format
            
        Returns:
            dict: Exchange rates with currency codes as keys
        """
        cache_key = f"{base_currency}_{target_date}"
        
        if cache_key in self.cache:
            logger.debug(f"Using cached exchange rates for {cache_key}")
            return self.cache[cache_key]
        
        url = f"{self.base_url}/{self.api_key}/history/{base_currency}/{target_date}"
        
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('result') == 'success':
                rates = data.get('conversion_rates', {})
                self.cache[cache_key] = rates
                logger.info(f"Fetched exchange rates for {base_currency} on {target_date}")
                return rates
            else:
                logger.error(f"API error: {data.get('error-type', 'Unknown error')}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed for {url}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching exchange rates: {str(e)}")
            return None
    
    def convert_currency(self, amount, from_currency, to_currency, year, month):
        """
        Convert currency amount using historical rates for the last day of the month
        
        Args:
            amount (float): Amount to convert
            from_currency (str): Source currency code
            to_currency (str): Target currency code
            year (int): Year
            month (int): Month
            
        Returns:
            float: Converted amount or None if conversion fails
        """
        if from_currency == to_currency:
            return amount
        
        # Get last day of the month
        target_date = self.get_last_day_of_month(year, month)
        
        # Fetch rates with from_currency as base
        rates = self.fetch_historical_rates(from_currency, target_date)
        
        if rates and to_currency in rates:
            converted_amount = amount * rates[to_currency]
            logger.debug(f"Converted {amount} {from_currency} to {converted_amount:.2f} {to_currency} on {target_date}")
            return converted_amount
        else:
            logger.warning(f"Could not convert {from_currency} to {to_currency} for {target_date}")
            return None
    
    def get_monthly_exchange_rates(self, currencies, years, months):
        """
        Get exchange rates for multiple currencies and months
        
        Args:
            currencies (list): List of currency codes
            years (list): List of years
            months (list): List of months (1-12)
            
        Returns:
            DataFrame: Exchange rates with columns [year, month, currency, rate_to_eur]
        """
        exchange_data = []
        
        for year in years:
            for month in months:
                target_date = self.get_last_day_of_month(year, month)
                
                for currency in currencies:
                    if currency == config.BASE_CURRENCY:
                        # Base currency rate is always 1
                        exchange_data.append({
                            'year': year,
                            'month': f"{month:02d}",
                            'currency': currency,
                            'rate_to_eur': 1.0,
                            'date': target_date
                        })
                    else:
                        # Get rate from currency to EUR
                        rates = self.fetch_historical_rates(currency, target_date)
                        if rates and config.BASE_CURRENCY in rates:
                            rate = rates[config.BASE_CURRENCY]
                            exchange_data.append({
                                'year': year,
                                'month': f"{month:02d}",
                                'currency': currency,
                                'rate_to_eur': rate,
                                'date': target_date
                            })
        
        return pd.DataFrame(exchange_data)

# Example usage for testing
if __name__ == "__main__":
    # Note: You need to set your API key in the environment or config
    service = ExchangeRateService()
    
    # Test conversion
    result = service.convert_currency(100, 'USD', 'EUR', 2023, 1)
    if result:
        print(f"100 USD = {result:.2f} EUR")
    
    # Test getting monthly rates
    rates_df = service.get_monthly_exchange_rates(['USD', 'EUR'], [2023], [1, 2])
    print(rates_df)
