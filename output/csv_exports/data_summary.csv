layer,filename,table_name,rows,columns,column_names,memory_usage_mb,file_size_mb
bronze,expenses.parquet,expenses,3456,10,"id, year, month, department, category, amount_local, currency, project_code, source_file, amount_eur",1.18,0.04
bronze,projects.parquet,projects,8,5,"id, name, country, currency, source_file",0.0,0.0
bronze,budgets.parquet,budgets,3456,9,"id, year, month, department, category, budget_eur, version, project_code, source_file",1.04,0.03
silver,expenses_clean.parquet,expenses_clean,2880,13,"id, year, month, department, category, amount_local, currency, project_code, source_file, amount_eur, processed_at, project_currency, source_currency",1.31,0.07
silver,projects_clean.parquet,projects_clean,8,6,"id, name, country, currency, source_file, processed_at",0.0,0.0
silver,budgets_clean.parquet,budgets_clean,3456,10,"id, year, month, department, category, budget_eur, version, project_code, source_file, processed_at",1.21,0.03
gold,fact_expenses.parquet,fact_expenses,2880,6,"project_id, time_key, category_key, amount_local, amount_eur, currency",0.53,0.07
gold,dim_categories.parquet,dim_categories,12,3,"category_id, department, category",0.0,0.0
gold,monthly_expenses_by_project.parquet,monthly_expenses_by_project,240,3,"project_id, time_key, total_expenses_eur",0.03,0.0
gold,dim_projects.parquet,dim_projects,8,5,"project_id, project_name, country, project_currency, region",0.0,0.0
gold,dim_time.parquet,dim_time,36,8,"time_key, year, month, month_str, quarter, year_month, month_name, quarter_name",0.01,0.01
gold,budget_vs_actual.parquet,budget_vs_actual,3456,7,"project_id, time_key, category_key, budget_eur, amount_eur, variance_eur, variance_pct",0.49,0.11
gold,fact_budgets.parquet,fact_budgets,3456,5,"project_id, time_key, category_key, budget_eur, budget_version",0.58,0.03
gold,monthly_expenses_by_category.parquet,monthly_expenses_by_category,360,3,"category_key, time_key, total_expenses_eur",0.02,0.01
