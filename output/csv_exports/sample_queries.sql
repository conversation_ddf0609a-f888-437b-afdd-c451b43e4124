
-- Sample SQL Queries for MSF Data Analysis
-- Use these queries with any SQL tool that can read CSV files

-- 1. Project Overview
SELECT 
    project_name,
    country,
    region,
    project_currency
FROM dim_projects;

-- 2. Monthly Expense Trends
SELECT 
    year,
    month_name,
    SUM(amount_eur) as total_expenses_eur
FROM fact_expenses f
JOIN dim_time t ON f.time_key = t.time_key
GROUP BY year, month, month_name
ORDER BY year, month;

-- 3. Top Expense Categories
SELECT 
    c.department,
    c.category,
    COUNT(*) as transaction_count,
    SUM(f.amount_eur) as total_expenses_eur,
    AVG(f.amount_eur) as avg_expense_eur
FROM fact_expenses f
JOIN dim_categories c ON f.category_key = c.category_id
GROUP BY c.department, c.category
ORDER BY total_expenses_eur DESC;

-- 4. Budget vs Actual by Project
SELECT 
    p.project_name,
    SUM(b.budget_eur) as total_budget,
    SUM(b.amount_eur) as total_actual,
    SUM(b.variance_eur) as variance,
    AVG(b.variance_pct) as avg_variance_pct
FROM budget_vs_actual b
JOIN dim_projects p ON b.project_id = p.project_id
GROUP BY p.project_name
ORDER BY variance DESC;

-- 5. Regional Comparison
SELECT 
    p.region,
    COUNT(DISTINCT p.project_id) as project_count,
    SUM(f.amount_eur) as total_expenses
FROM fact_expenses f
JOIN dim_projects p ON f.project_id = p.project_id
GROUP BY p.region
ORDER BY total_expenses DESC;

-- 6. Monthly Expenses by Project
SELECT 
    p.project_name,
    t.year_month,
    SUM(f.amount_eur) as monthly_expenses
FROM fact_expenses f
JOIN dim_projects p ON f.project_id = p.project_id
JOIN dim_time t ON f.time_key = t.time_key
GROUP BY p.project_name, t.year_month
ORDER BY p.project_name, t.year_month;

-- 7. Currency Distribution
SELECT 
    currency,
    COUNT(*) as transaction_count,
    SUM(amount_local) as total_local_amount,
    SUM(amount_eur) as total_eur_amount
FROM fact_expenses
GROUP BY currency
ORDER BY transaction_count DESC;

-- 8. Department Spending Analysis
SELECT 
    c.department,
    COUNT(DISTINCT c.category) as category_count,
    COUNT(*) as transaction_count,
    SUM(f.amount_eur) as total_spending,
    AVG(f.amount_eur) as avg_transaction_amount
FROM fact_expenses f
JOIN dim_categories c ON f.category_key = c.category_id
GROUP BY c.department
ORDER BY total_spending DESC;
