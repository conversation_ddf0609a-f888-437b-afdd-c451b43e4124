layer,filename,table_name,rows,columns,column_names,file_size_kb
bronze,expenses.csv,expenses,3456,10,"id, year, month, department, category...",206.4
bronze,projects.csv,projects,8,5,"id, name, country, currency, source_file",0.4
bronze,budgets.csv,budgets,3456,9,"id, year, month, department, category...",208.1
silver,expenses_clean.csv,expenses_clean,2880,13,"id, year, month, department, category...",312.0
silver,projects_clean.csv,projects_clean,8,6,"id, name, country, currency, source_file...",0.6
silver,budgets_clean.csv,budgets_clean,3456,10,"id, year, month, department, category...",301.8
gold,fact_expenses.csv,fact_expenses,2880,6,"project_id, time_key, category_key, amount_local, amount_eur...",131.4
gold,dim_categories.csv,dim_categories,12,3,"category_id, department, category",0.3
gold,monthly_expenses_by_project.csv,monthly_expenses_by_project,240,3,"project_id, time_key, total_expenses_eur",6.4
gold,dim_projects.csv,dim_projects,8,6,"project_id, project_name, country, project_currency, region...",0.5
gold,dim_time.csv,dim_time,36,11,"time_key, year, month, month_str, quarter...",2.4
gold,budget_vs_actual.csv,budget_vs_actual,3456,7,"project_id, time_key, category_key, budget_eur, amount_eur...",229.6
gold,fact_budgets.csv,fact_budgets,3456,5,"project_id, time_key, category_key, budget_eur, budget_version",84.9
gold,monthly_expenses_by_category.csv,monthly_expenses_by_category,360,3,"category_key, time_key, total_expenses_eur",9.8
