"""
Generate Architecture Diagram as PNG
"""
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

def create_architecture_diagram():
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    
    # Define colors
    colors = {
        'source': '#e1f5fe',
        'bronze': '#fff3e0', 
        'silver': '#fff3e0',
        'gold': '#e8f5e8',
        'output': '#f3e5f5',
        'analytics': '#fce4ec'
    }
    
    # Source layer
    ax.add_patch(FancyBboxPatch((0.5, 8), 2, 1, boxstyle="round,pad=0.1", 
                                facecolor=colors['source'], edgecolor='black', linewidth=1))
    ax.text(1.5, 8.5, 'SQLite DBs\n(BE01.db, etc.)', ha='center', va='center', fontsize=9, weight='bold')
    
    ax.add_patch(FancyBboxPatch((3, 8), 2, 1, boxstyle="round,pad=0.1", 
                                facecolor=colors['source'], edgecolor='black', linewidth=1))
    ax.text(4, 8.5, 'CSV Files\n(*_budget.csv)', ha='center', va='center', fontsize=9, weight='bold')
    
    # Bronze layer
    ax.add_patch(FancyBboxPatch((1.5, 6.5), 2.5, 1, boxstyle="round,pad=0.1", 
                                facecolor=colors['bronze'], edgecolor='black', linewidth=1))
    ax.text(2.75, 7, 'Bronze Layer\nRaw Data Extraction', ha='center', va='center', fontsize=10, weight='bold')
    
    # Silver layer
    ax.add_patch(FancyBboxPatch((1.5, 5), 2.5, 1, boxstyle="round,pad=0.1", 
                                facecolor=colors['silver'], edgecolor='black', linewidth=1))
    ax.text(2.75, 5.5, 'Silver Layer\nCleaning & Currency\nConversion', ha='center', va='center', fontsize=10, weight='bold')
    
    # Gold layer components
    ax.add_patch(FancyBboxPatch((0.5, 3), 1.8, 1, boxstyle="round,pad=0.1", 
                                facecolor=colors['gold'], edgecolor='black', linewidth=1))
    ax.text(1.4, 3.5, 'Dimension\nTables', ha='center', va='center', fontsize=9, weight='bold')
    
    ax.add_patch(FancyBboxPatch((2.5, 3), 1.8, 1, boxstyle="round,pad=0.1", 
                                facecolor=colors['gold'], edgecolor='black', linewidth=1))
    ax.text(3.4, 3.5, 'Fact\nTables', ha='center', va='center', fontsize=9, weight='bold')
    
    ax.add_patch(FancyBboxPatch((4.5, 3), 1.8, 1, boxstyle="round,pad=0.1", 
                                facecolor=colors['gold'], edgecolor='black', linewidth=1))
    ax.text(5.4, 3.5, 'Summary\nTables', ha='center', va='center', fontsize=9, weight='bold')
    
    # Data warehouse
    ax.add_patch(FancyBboxPatch((1.5, 1.5), 2.5, 1, boxstyle="round,pad=0.1", 
                                facecolor=colors['output'], edgecolor='black', linewidth=1))
    ax.text(2.75, 2, 'SQLite Data Warehouse\n& Parquet Files', ha='center', va='center', fontsize=10, weight='bold')
    
    # Analytics outputs
    ax.add_patch(FancyBboxPatch((0.5, 0), 1.8, 1, boxstyle="round,pad=0.1", 
                                facecolor=colors['analytics'], edgecolor='black', linewidth=1))
    ax.text(1.4, 0.5, 'Power BI\nDashboards', ha='center', va='center', fontsize=9, weight='bold')
    
    ax.add_patch(FancyBboxPatch((2.5, 0), 1.8, 1, boxstyle="round,pad=0.1", 
                                facecolor=colors['analytics'], edgecolor='black', linewidth=1))
    ax.text(3.4, 0.5, 'Python\nAnalytics', ha='center', va='center', fontsize=9, weight='bold')
    
    ax.add_patch(FancyBboxPatch((4.5, 0), 1.8, 1, boxstyle="round,pad=0.1", 
                                facecolor=colors['analytics'], edgecolor='black', linewidth=1))
    ax.text(5.4, 0.5, 'CSV\nExports', ha='center', va='center', fontsize=9, weight='bold')
    
    # Add arrows
    arrows = [
        ((1.5, 8.2), (2.5, 7.3)),  # SQLite to Bronze
        ((4, 8.2), (3, 7.3)),      # CSV to Bronze
        ((2.75, 6.5), (2.75, 6)),  # Bronze to Silver
        ((2.75, 5), (2.75, 4.2)),  # Silver to Gold
        ((2.75, 3), (2.75, 2.5)),  # Gold to Warehouse
        ((2.2, 1.5), (1.8, 1)),    # Warehouse to Power BI
        ((2.75, 1.5), (2.75, 1)),  # Warehouse to Analytics
        ((3.3, 1.5), (3.8, 1)),    # Warehouse to CSV
    ]
    
    for start, end in arrows:
        ax.annotate('', xy=end, xytext=start,
                   arrowprops=dict(arrowstyle='->', lw=1.5, color='#333333'))
    
    # Add side annotations
    ax.text(7, 8.5, 'Data Sources', fontsize=12, weight='bold', rotation=90, va='center')
    ax.text(7, 6, 'Medallion\nArchitecture', fontsize=12, weight='bold', rotation=90, va='center')
    ax.text(7, 2, 'Analytics\nLayer', fontsize=12, weight='bold', rotation=90, va='center')
    
    # Set limits and remove axes
    ax.set_xlim(0, 8)
    ax.set_ylim(-0.5, 9.5)
    ax.set_aspect('equal')
    ax.axis('off')
    
    # Add title
    plt.title('MSF Data Engineering Architecture\nMedallion Architecture Implementation', 
              fontsize=16, weight='bold', pad=20)
    
    plt.tight_layout()
    plt.savefig('output/msf_architecture_diagram.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Architecture diagram saved as: output/msf_architecture_diagram.png")

if __name__ == "__main__":
    create_architecture_diagram()
