"""
Parquet to CSV Converter
This utility converts all Parquet files from the medallion architecture layers to CSV format
for easy inspection and analysis.
"""
import pandas as pd
from pathlib import Path
import config
from loguru import logger
import sys

def setup_logging():
    """Configure logging for the converter"""
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:HH:mm:ss} | {level} | {message}")

def convert_parquet_to_csv(parquet_path, csv_path):
    """
    Convert a single Parquet file to CSV
    
    Args:
        parquet_path (Path): Path to the Parquet file
        csv_path (Path): Path for the output CSV file
    """
    try:
        # Read Parquet file
        df = pd.read_parquet(parquet_path)
        
        # Convert to CSV
        df.to_csv(csv_path, index=False)
        
        logger.info(f"Converted {parquet_path.name} -> {csv_path.name} ({len(df)} rows, {len(df.columns)} columns)")
        
        # Print sample of data for quick inspection
        print(f"\n--- Sample from {parquet_path.name} ---")
        print(f"Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        if not df.empty:
            print("First 3 rows:")
            print(df.head(3).to_string())
        print("-" * 50)
        
        return True
        
    except Exception as e:
        logger.error(f"Error converting {parquet_path}: {str(e)}")
        return False

def convert_layer_files(layer_dir, output_dir, layer_name):
    """
    Convert all Parquet files in a layer directory to CSV
    
    Args:
        layer_dir (Path): Directory containing Parquet files
        output_dir (Path): Directory for CSV output
        layer_name (str): Name of the layer (bronze, silver, gold)
    """
    if not layer_dir.exists():
        logger.warning(f"{layer_name} layer directory not found: {layer_dir}")
        return
    
    # Create output directory for this layer
    layer_output_dir = output_dir / layer_name
    layer_output_dir.mkdir(exist_ok=True)
    
    # Find all Parquet files
    parquet_files = list(layer_dir.glob("*.parquet"))
    
    if not parquet_files:
        logger.warning(f"No Parquet files found in {layer_name} layer")
        return
    
    logger.info(f"Converting {len(parquet_files)} files from {layer_name} layer")
    
    success_count = 0
    for parquet_file in parquet_files:
        # Create CSV filename
        csv_filename = parquet_file.stem + ".csv"
        csv_path = layer_output_dir / csv_filename
        
        if convert_parquet_to_csv(parquet_file, csv_path):
            success_count += 1
    
    logger.info(f"Successfully converted {success_count}/{len(parquet_files)} files from {layer_name} layer")

def create_data_summary():
    """Create a summary CSV with information about all datasets"""
    summary_data = []
    
    # Check all layers
    for layer_name in ['bronze', 'silver', 'gold']:
        layer_dir = config.OUTPUT_DIR / layer_name
        if layer_dir.exists():
            for parquet_file in layer_dir.glob("*.parquet"):
                try:
                    df = pd.read_parquet(parquet_file)
                    summary_data.append({
                        'layer': layer_name,
                        'filename': parquet_file.name,
                        'table_name': parquet_file.stem,
                        'rows': len(df),
                        'columns': len(df.columns),
                        'column_names': ', '.join(df.columns.tolist()),
                        'memory_usage_mb': round(df.memory_usage(deep=True).sum() / 1024 / 1024, 2),
                        'file_size_mb': round(parquet_file.stat().st_size / 1024 / 1024, 2)
                    })
                except Exception as e:
                    logger.error(f"Error reading {parquet_file}: {str(e)}")
    
    if summary_data:
        summary_df = pd.DataFrame(summary_data)
        summary_path = config.OUTPUT_DIR / "csv_exports" / "data_summary.csv"
        summary_df.to_csv(summary_path, index=False)
        
        logger.info(f"Created data summary: {summary_path}")
        print(f"\n--- DATA SUMMARY ---")
        print(summary_df.to_string(index=False))
        print("-" * 80)

def create_sample_queries():
    """Create a file with sample SQL queries for the converted data"""
    sample_queries = """
-- Sample SQL Queries for MSF Data Analysis
-- Use these queries with any SQL tool that can read CSV files

-- 1. Project Overview
SELECT 
    project_name,
    country,
    region,
    project_currency
FROM dim_projects;

-- 2. Monthly Expense Trends
SELECT 
    year,
    month_name,
    SUM(amount_eur) as total_expenses_eur
FROM fact_expenses f
JOIN dim_time t ON f.time_key = t.time_key
GROUP BY year, month, month_name
ORDER BY year, month;

-- 3. Top Expense Categories
SELECT 
    c.department,
    c.category,
    COUNT(*) as transaction_count,
    SUM(f.amount_eur) as total_expenses_eur,
    AVG(f.amount_eur) as avg_expense_eur
FROM fact_expenses f
JOIN dim_categories c ON f.category_key = c.category_id
GROUP BY c.department, c.category
ORDER BY total_expenses_eur DESC;

-- 4. Budget vs Actual by Project
SELECT 
    p.project_name,
    SUM(b.budget_eur) as total_budget,
    SUM(b.amount_eur) as total_actual,
    SUM(b.variance_eur) as variance,
    AVG(b.variance_pct) as avg_variance_pct
FROM budget_vs_actual b
JOIN dim_projects p ON b.project_id = p.project_id
GROUP BY p.project_name
ORDER BY variance DESC;

-- 5. Regional Comparison
SELECT 
    p.region,
    COUNT(DISTINCT p.project_id) as project_count,
    SUM(f.amount_eur) as total_expenses
FROM fact_expenses f
JOIN dim_projects p ON f.project_id = p.project_id
GROUP BY p.region
ORDER BY total_expenses DESC;

-- 6. Monthly Expenses by Project
SELECT 
    p.project_name,
    t.year_month,
    SUM(f.amount_eur) as monthly_expenses
FROM fact_expenses f
JOIN dim_projects p ON f.project_id = p.project_id
JOIN dim_time t ON f.time_key = t.time_key
GROUP BY p.project_name, t.year_month
ORDER BY p.project_name, t.year_month;

-- 7. Currency Distribution
SELECT 
    currency,
    COUNT(*) as transaction_count,
    SUM(amount_local) as total_local_amount,
    SUM(amount_eur) as total_eur_amount
FROM fact_expenses
GROUP BY currency
ORDER BY transaction_count DESC;

-- 8. Department Spending Analysis
SELECT 
    c.department,
    COUNT(DISTINCT c.category) as category_count,
    COUNT(*) as transaction_count,
    SUM(f.amount_eur) as total_spending,
    AVG(f.amount_eur) as avg_transaction_amount
FROM fact_expenses f
JOIN dim_categories c ON f.category_key = c.category_id
GROUP BY c.department
ORDER BY total_spending DESC;
"""
    
    queries_path = config.OUTPUT_DIR / "csv_exports" / "sample_queries.sql"
    with open(queries_path, 'w') as f:
        f.write(sample_queries)
    
    logger.info(f"Created sample queries file: {queries_path}")

def main():
    """Main function to convert all Parquet files to CSV"""
    setup_logging()
    
    logger.info("Starting Parquet to CSV conversion")
    logger.info("=" * 60)
    
    # Create main CSV export directory
    csv_export_dir = config.OUTPUT_DIR / "csv_exports"
    csv_export_dir.mkdir(exist_ok=True)
    
    # Convert each layer
    convert_layer_files(config.BRONZE_DIR, csv_export_dir, "bronze")
    convert_layer_files(config.SILVER_DIR, csv_export_dir, "silver")
    convert_layer_files(config.GOLD_DIR, csv_export_dir, "gold")
    
    # Create data summary
    create_data_summary()
    
    # Create sample queries
    create_sample_queries()
    
    logger.info("=" * 60)
    logger.info("Conversion completed!")
    logger.info(f"All CSV files saved to: {csv_export_dir}")
    logger.info("Files created:")
    
    # List all created files
    for csv_file in csv_export_dir.rglob("*.csv"):
        relative_path = csv_file.relative_to(csv_export_dir)
        file_size = csv_file.stat().st_size / 1024  # KB
        logger.info(f"  - {relative_path} ({file_size:.1f} KB)")
    
    # List SQL file
    sql_file = csv_export_dir / "sample_queries.sql"
    if sql_file.exists():
        logger.info(f"  - sample_queries.sql")
    
    print(f"\n📁 CSV files are ready for inspection in: {csv_export_dir}")
    print("📊 Check data_summary.csv for an overview of all datasets")
    print("🔍 Use sample_queries.sql for analysis examples")

if __name__ == "__main__":
    main()
