"""
Silver Layer - Data Transformation
This module handles data cleaning, standardization, and currency conversion
"""
import pandas as pd
from loguru import logger
import config
from exchange_rate_service import ExchangeRateService
from mock_exchange_service import MockExchangeRateService

class SilverLayerProcessor:
    """Processes data for the Silver layer with cleaning and standardization"""
    
    def __init__(self):
        # Use mock service if API key is not properly configured
        if config.EXCHANGE_RATE_API_KEY == "your_api_key_here" or config.EXCHANGE_RATE_API_KEY == "test_key_placeholder":
            logger.info("Using mock exchange rate service for testing")
            self.exchange_service = MockExchangeRateService()
        else:
            logger.info("Using real exchange rate service")
            self.exchange_service = ExchangeRateService()
    
    def load_bronze_data(self):
        """Load data from bronze layer"""
        try:
            projects_df = pd.read_parquet(config.BRONZE_DIR / "projects.parquet")
            expenses_df = pd.read_parquet(config.BRONZE_DIR / "expenses.parquet")
            budgets_df = pd.read_parquet(config.BRONZE_DIR / "budgets.parquet")
            
            logger.info(f"Loaded bronze data: {len(projects_df)} projects, {len(expenses_df)} expenses, {len(budgets_df)} budgets")
            return projects_df, expenses_df, budgets_df
        
        except Exception as e:
            logger.error(f"Error loading bronze data: {str(e)}")
            return None, None, None
    
    def clean_projects_data(self, projects_df):
        """Clean and standardize projects data"""
        if projects_df.empty:
            return projects_df
        
        # Create a copy to avoid modifying original
        df = projects_df.copy()
        
        # Standardize column names
        df.columns = df.columns.str.lower().str.strip()
        
        # Clean text fields
        text_columns = ['name', 'country', 'currency']
        for col in text_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()
        
        # Standardize currency codes
        if 'currency' in df.columns:
            df['currency'] = df['currency'].str.upper()
        
        # Add processing timestamp
        df['processed_at'] = pd.Timestamp.now()
        
        logger.info(f"Cleaned projects data: {len(df)} records")
        return df
    
    def clean_expenses_data(self, expenses_df):
        """Clean and standardize expenses data"""
        if expenses_df.empty:
            return expenses_df
        
        # Create a copy to avoid modifying original
        df = expenses_df.copy()
        
        # Standardize column names
        df.columns = df.columns.str.lower().str.strip()
        
        # Clean text fields
        text_columns = ['month', 'department', 'category', 'currency', 'project_code']
        for col in text_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()
        
        # Standardize currency codes
        if 'currency' in df.columns:
            df['currency'] = df['currency'].str.upper()
        
        # Ensure numeric fields are properly typed
        if 'amount_local' in df.columns:
            df['amount_local'] = pd.to_numeric(df['amount_local'], errors='coerce')
        
        # Standardize month format (ensure 2 digits)
        if 'month' in df.columns:
            df['month'] = df['month'].str.zfill(2)
        
        # Remove records with missing critical data
        initial_count = len(df)
        df = df.dropna(subset=['year', 'month', 'amount_local'])
        final_count = len(df)
        
        if initial_count != final_count:
            logger.warning(f"Removed {initial_count - final_count} records with missing critical data")
        
        # Add processing timestamp
        df['processed_at'] = pd.Timestamp.now()
        
        logger.info(f"Cleaned expenses data: {len(df)} records")
        return df
    
    def clean_budgets_data(self, budgets_df):
        """Clean and standardize budgets data"""
        if budgets_df.empty:
            return budgets_df
        
        # Create a copy to avoid modifying original
        df = budgets_df.copy()
        
        # Standardize column names
        df.columns = df.columns.str.lower().str.strip()
        
        # Clean text fields
        text_columns = ['month', 'department', 'category', 'version', 'project_code']
        for col in text_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()
        
        # Ensure numeric fields are properly typed
        if 'budget_eur' in df.columns:
            df['budget_eur'] = pd.to_numeric(df['budget_eur'], errors='coerce')
        
        # Standardize month format (ensure 2 digits)
        if 'month' in df.columns:
            df['month'] = df['month'].str.zfill(2)
        
        # Remove records with missing critical data
        initial_count = len(df)
        df = df.dropna(subset=['year', 'month', 'budget_eur'])
        final_count = len(df)
        
        if initial_count != final_count:
            logger.warning(f"Removed {initial_count - final_count} budget records with missing critical data")
        
        # Add processing timestamp
        df['processed_at'] = pd.Timestamp.now()
        
        logger.info(f"Cleaned budgets data: {len(df)} records")
        return df
    
    def convert_currencies(self, expenses_df, projects_df):
        """Convert all expenses to EUR using historical exchange rates"""
        if expenses_df.empty or projects_df.empty:
            return expenses_df
        
        # Create project currency mapping
        project_currency_map = projects_df.set_index('id')['currency'].to_dict()
        
        # Add currency information to expenses
        df = expenses_df.copy()
        df['project_currency'] = df['project_code'].map(project_currency_map)
        
        # Use expense currency if available, otherwise use project currency
        df['source_currency'] = df['currency'].fillna(df['project_currency'])
        
        # Initialize EUR amount column
        df['amount_eur'] = 0.0
        
        # Get unique currency-year-month combinations for batch processing
        unique_combinations = df[['source_currency', 'year', 'month']].drop_duplicates()
        
        # Fetch exchange rates for all combinations
        exchange_rates = {}
        for _, row in unique_combinations.iterrows():
            currency = row['source_currency']
            year = int(row['year'])
            month = int(row['month'])
            
            if currency == config.BASE_CURRENCY:
                rate = 1.0
            else:
                # Get rate from source currency to EUR
                rates = self.exchange_service.fetch_historical_rates(
                    currency, 
                    self.exchange_service.get_last_day_of_month(year, month)
                )
                rate = rates.get(config.BASE_CURRENCY, None) if rates else None
            
            exchange_rates[(currency, year, month)] = rate
        
        # Apply currency conversion
        conversion_errors = 0
        for idx, row in df.iterrows():
            currency = row['source_currency']
            year = int(row['year'])
            month = int(row['month'])
            amount = row['amount_local']
            
            rate = exchange_rates.get((currency, year, month))
            
            if rate is not None:
                df.at[idx, 'amount_eur'] = amount * rate
            else:
                df.at[idx, 'amount_eur'] = None
                conversion_errors += 1
        
        if conversion_errors > 0:
            logger.warning(f"Failed to convert {conversion_errors} expense records")
        
        logger.info(f"Converted currencies for {len(df) - conversion_errors} expense records")
        return df
    
    def process_silver_layer(self):
        """Main processing function for silver layer"""
        logger.info("Starting Silver Layer processing")
        
        # Load bronze data
        projects_df, expenses_df, budgets_df = self.load_bronze_data()
        
        if projects_df is None:
            logger.error("Failed to load bronze data")
            return
        
        # Clean data
        clean_projects = self.clean_projects_data(projects_df)
        clean_expenses = self.clean_expenses_data(expenses_df)
        clean_budgets = self.clean_budgets_data(budgets_df)
        
        # Convert currencies
        expenses_with_eur = self.convert_currencies(clean_expenses, clean_projects)
        
        # Save to silver layer
        clean_projects.to_parquet(config.SILVER_DIR / "projects_clean.parquet")
        expenses_with_eur.to_parquet(config.SILVER_DIR / "expenses_clean.parquet")
        clean_budgets.to_parquet(config.SILVER_DIR / "budgets_clean.parquet")
        
        logger.info("Silver Layer processing completed")
        
        return clean_projects, expenses_with_eur, clean_budgets

if __name__ == "__main__":
    processor = SilverLayerProcessor()
    processor.process_silver_layer()
