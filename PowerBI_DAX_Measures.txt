// MSF Power BI DAX Measures
// Copy and paste these measures into your Power BI model

// ===========================================
// BASIC MEASURES
// ===========================================

Total Budget = 
SUM(fact_budgets[budget_eur])

Total Expense = 
SUM(fact_expenses[amount_eur])

Monthly Budget = 
SUM(fact_budgets[budget_eur])

Monthly Expense = 
SUM(fact_expenses[amount_eur])

Budget Variance = 
[Total Budget] - [Total Expense]

Variance Percentage = 
DIVIDE([Budget Variance], [Total Budget], 0) * 100

// ===========================================
// ACCUMULATED MEASURES
// ===========================================

Accumulated Budget = 
CALCULATE(
    SUM(fact_budgets[budget_eur]),
    FILTER(
        ALL(dim_time[time_key]),
        dim_time[time_key] <= MAX(dim_time[time_key])
    )
)

Accumulated Expenses = 
CALCULATE(
    SUM(fact_expenses[amount_eur]),
    FILTER(
        ALL(dim_time[time_key]),
        dim_time[time_key] <= MAX(dim_time[time_key])
    )
)

Accumulated Budget Variance = 
[Accumulated Budget] - [Accumulated Expenses]

// ===========================================
// TIME INTELLIGENCE MEASURES
// ===========================================

Previous Month Budget = 
CALCULATE(
    [Total Budget],
    PREVIOUSMONTH(dim_time[year_month])
)

Previous Month Expense = 
CALCULATE(
    [Total Expense],
    PREVIOUSMONTH(dim_time[year_month])
)

Budget Growth = 
[Total Budget] - [Previous Month Budget]

Expense Growth = 
[Total Expense] - [Previous Month Expense]

// ===========================================
// CALCULATED COLUMNS (Add to dim_time table)
// ===========================================

// Month ID (for display formatting)
Month ID = 
FORMAT(
    DATE(dim_time[year], dim_time[month], 1),
    "mmm yyyy"
)

// Quarter Display
Quarter Display = 
"Q" & dim_time[quarter] & " " & dim_time[year]

// ===========================================
// CONDITIONAL FORMATTING MEASURES
// ===========================================

Budget Status = 
IF(
    [Total Expense] <= [Total Budget],
    "Under Budget",
    "Over Budget"
)

Variance Color = 
IF(
    [Budget Variance] >= 0,
    "Green",
    "Red"
)

// ===========================================
// PERCENTAGE MEASURES
// ===========================================

Budget Utilization = 
DIVIDE([Total Expense], [Total Budget], 0) * 100

Department Budget Share = 
DIVIDE(
    [Total Budget],
    CALCULATE([Total Budget], ALL(dim_categories[department])),
    0
) * 100

Project Budget Share = 
DIVIDE(
    [Total Budget],
    CALCULATE([Total Budget], ALL(dim_projects[project_name])),
    0
) * 100

// ===========================================
// RANKING MEASURES
// ===========================================

Department Expense Rank = 
RANKX(
    ALL(dim_categories[department]),
    [Total Expense],
    ,
    DESC
)

Project Expense Rank = 
RANKX(
    ALL(dim_projects[project_name]),
    [Total Expense],
    ,
    DESC
)

// ===========================================
// AVERAGE MEASURES
// ===========================================

Average Monthly Budget = 
AVERAGEX(
    VALUES(dim_time[time_key]),
    [Monthly Budget]
)

Average Monthly Expense = 
AVERAGEX(
    VALUES(dim_time[time_key]),
    [Monthly Expense]
)

// ===========================================
// COUNT MEASURES
// ===========================================

Active Projects = 
DISTINCTCOUNT(fact_expenses[project_id])

Active Departments = 
DISTINCTCOUNT(fact_expenses[category_key])

Months with Data = 
DISTINCTCOUNT(fact_expenses[time_key])

// ===========================================
// FORMATTING NOTES
// ===========================================

/*
FORMATTING RECOMMENDATIONS:

1. Currency Measures (Budget, Expense, Variance):
   - Format: Currency
   - Symbol: € (Euro)
   - Decimal places: 0 for millions, 2 for smaller amounts
   - Display units: Auto or Millions

2. Percentage Measures:
   - Format: Percentage
   - Decimal places: 1

3. Count Measures:
   - Format: Whole number
   - No decimal places

4. Date Formatting:
   - Use "mmm yyyy" format for month display
   - Example: "Jan 2023", "Jul 2024"

5. Color Coding:
   - Budget: Light blue (#5B9BD5)
   - Expenses: Dark blue (#1F4E79)
   - Over budget: Red (#C5504B)
   - Under budget: Green (#70AD47)
*/
