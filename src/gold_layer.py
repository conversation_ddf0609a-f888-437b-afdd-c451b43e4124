"""
Gold Layer - Data Modeling
This module creates analytical data models optimized for reporting and analysis
"""
import pandas as pd
from loguru import logger
import config
from sqlalchemy import create_engine, text

class GoldLayerProcessor:
    """Creates analytical data models for the Gold layer"""
    
    def __init__(self):
        self.engine = create_engine(config.DATABASE_URL)
    
    def load_silver_data(self):
        """Load cleaned data from silver layer"""
        try:
            projects_df = pd.read_parquet(config.SILVER_DIR / "projects_clean.parquet")
            expenses_df = pd.read_parquet(config.SILVER_DIR / "expenses_clean.parquet")
            budgets_df = pd.read_parquet(config.SILVER_DIR / "budgets_clean.parquet")
            
            logger.info(f"Loaded silver data: {len(projects_df)} projects, {len(expenses_df)} expenses, {len(budgets_df)} budgets")
            return projects_df, expenses_df, budgets_df
        
        except Exception as e:
            logger.error(f"Error loading silver data: {str(e)}")
            return None, None, None
    
    def create_dim_projects(self, projects_df):
        """Create projects dimension table"""
        if projects_df.empty:
            return pd.DataFrame()
        
        dim_projects = projects_df[['id', 'name', 'country', 'currency']].copy()
        dim_projects.columns = ['project_id', 'project_name', 'country', 'project_currency']
        
        # Add derived fields
        dim_projects['region'] = dim_projects['country'].map({
            'Belgium': 'Europe',
            'Burkina Faso': 'Africa',
            'Kenya': 'Africa',
            'Senegal': 'Africa'
        }).fillna('Other')

        # Add Power BI friendly name field (alias for project_name)
        dim_projects['name'] = dim_projects['project_name']
        
        logger.info(f"Created dim_projects with {len(dim_projects)} records")
        return dim_projects
    
    def create_dim_time(self):
        """Create time dimension table"""
        time_data = []
        
        for year in config.YEARS:
            for month in range(1, 13):
                month_str = f"{month:02d}"
                # Create proper date for Power BI
                date_obj = pd.to_datetime(f"{year}-{month_str}-01")
                time_data.append({
                    'time_key': f"{year}{month_str}",
                    'year': year,
                    'month': month,
                    'month_str': month_str,
                    'quarter': (month - 1) // 3 + 1,
                    'year_month': f"{year}-{month_str}",
                    'month_name': date_obj.strftime('%B'),
                    'quarter_name': f"Q{(month - 1) // 3 + 1}",
                    'date': date_obj,
                    'month_id': date_obj.strftime('%b %Y').lower(),  # For Power BI display (e.g., "jan 2023")
                    'key': f"{year}{month_str}"  # Alternative key field for Power BI
                })
        
        dim_time = pd.DataFrame(time_data)
        logger.info(f"Created dim_time with {len(dim_time)} records")
        return dim_time
    
    def create_dim_categories(self, expenses_df, budgets_df):
        """Create categories dimension table"""
        # Get unique department-category combinations from both expenses and budgets
        expense_cats = expenses_df[['department', 'category']].drop_duplicates()
        budget_cats = budgets_df[['department', 'category']].drop_duplicates()
        
        all_cats = pd.concat([expense_cats, budget_cats]).drop_duplicates()
        
        # Create category dimension
        dim_categories = all_cats.reset_index(drop=True)
        dim_categories['category_id'] = range(1, len(dim_categories) + 1)
        
        # Reorder columns
        dim_categories = dim_categories[['category_id', 'department', 'category']]
        
        logger.info(f"Created dim_categories with {len(dim_categories)} records")
        return dim_categories
    
    def create_fact_expenses(self, expenses_df, dim_projects, dim_time, dim_categories):
        """Create expenses fact table"""
        if expenses_df.empty:
            return pd.DataFrame()
        
        # Create fact table
        fact_expenses = expenses_df.copy()
        
        # Add time key
        fact_expenses['time_key'] = fact_expenses['year'].astype(str) + fact_expenses['month'].astype(str).str.zfill(2)
        
        # Add category key
        category_map = dim_categories.set_index(['department', 'category'])['category_id'].to_dict()
        fact_expenses['category_key'] = fact_expenses.apply(
            lambda row: category_map.get((row['department'], row['category']), None), axis=1
        )
        
        # Select and rename columns for fact table
        fact_columns = {
            'project_code': 'project_id',
            'time_key': 'time_key',
            'category_key': 'category_key',
            'amount_local': 'amount_local',
            'amount_eur': 'amount_eur',
            'source_currency': 'currency'
        }
        
        fact_expenses = fact_expenses[list(fact_columns.keys())].rename(columns=fact_columns)
        
        # Remove records with missing keys
        initial_count = len(fact_expenses)
        fact_expenses = fact_expenses.dropna(subset=['category_key', 'amount_eur'])
        final_count = len(fact_expenses)
        
        if initial_count != final_count:
            logger.warning(f"Removed {initial_count - final_count} expense records with missing keys")
        
        logger.info(f"Created fact_expenses with {len(fact_expenses)} records")
        return fact_expenses
    
    def create_fact_budgets(self, budgets_df, dim_projects, dim_time, dim_categories):
        """Create budgets fact table"""
        if budgets_df.empty:
            return pd.DataFrame()
        
        # Create fact table
        fact_budgets = budgets_df.copy()
        
        # Add time key
        fact_budgets['time_key'] = fact_budgets['year'].astype(str) + fact_budgets['month'].astype(str).str.zfill(2)
        
        # Add category key
        category_map = dim_categories.set_index(['department', 'category'])['category_id'].to_dict()
        fact_budgets['category_key'] = fact_budgets.apply(
            lambda row: category_map.get((row['department'], row['category']), None), axis=1
        )
        
        # Select and rename columns for fact table
        fact_columns = {
            'project_code': 'project_id',
            'time_key': 'time_key',
            'category_key': 'category_key',
            'budget_eur': 'budget_eur',
            'version': 'budget_version'
        }
        
        fact_budgets = fact_budgets[list(fact_columns.keys())].rename(columns=fact_columns)
        
        # Remove records with missing keys
        initial_count = len(fact_budgets)
        fact_budgets = fact_budgets.dropna(subset=['category_key', 'budget_eur'])
        final_count = len(fact_budgets)
        
        if initial_count != final_count:
            logger.warning(f"Removed {initial_count - final_count} budget records with missing keys")
        
        logger.info(f"Created fact_budgets with {len(fact_budgets)} records")
        return fact_budgets
    
    def create_summary_tables(self, fact_expenses, fact_budgets, dim_projects, dim_time, dim_categories):
        """Create summary tables for reporting"""
        summaries = {}
        
        # Monthly summary by project
        if not fact_expenses.empty:
            monthly_expenses = (fact_expenses
                              .groupby(['project_id', 'time_key'])
                              .agg({'amount_eur': 'sum'})
                              .reset_index())
            monthly_expenses.columns = ['project_id', 'time_key', 'total_expenses_eur']
            summaries['monthly_expenses_by_project'] = monthly_expenses
        
        # Monthly summary by category
        if not fact_expenses.empty:
            monthly_by_category = (fact_expenses
                                 .groupby(['category_key', 'time_key'])
                                 .agg({'amount_eur': 'sum'})
                                 .reset_index())
            monthly_by_category.columns = ['category_key', 'time_key', 'total_expenses_eur']
            summaries['monthly_expenses_by_category'] = monthly_by_category
        
        # Budget vs Actual comparison
        if not fact_expenses.empty and not fact_budgets.empty:
            budget_actual = pd.merge(
                fact_budgets.groupby(['project_id', 'time_key', 'category_key']).agg({'budget_eur': 'sum'}).reset_index(),
                fact_expenses.groupby(['project_id', 'time_key', 'category_key']).agg({'amount_eur': 'sum'}).reset_index(),
                on=['project_id', 'time_key', 'category_key'],
                how='outer'
            ).fillna(0)
            
            budget_actual['variance_eur'] = budget_actual['budget_eur'] - budget_actual['amount_eur']
            budget_actual['variance_pct'] = (budget_actual['variance_eur'] / budget_actual['budget_eur'] * 100).fillna(0)
            
            summaries['budget_vs_actual'] = budget_actual
        
        logger.info(f"Created {len(summaries)} summary tables")
        return summaries
    
    def save_to_database(self, tables_dict):
        """Save all tables to SQLite database"""
        try:
            for table_name, df in tables_dict.items():
                if not df.empty:
                    df.to_sql(table_name, self.engine, if_exists='replace', index=False)
                    logger.info(f"Saved {table_name} with {len(df)} records to database")
                else:
                    logger.warning(f"Skipped empty table: {table_name}")
        
        except Exception as e:
            logger.error(f"Error saving to database: {str(e)}")
    
    def save_to_parquet(self, tables_dict):
        """Save all tables to parquet files"""
        try:
            for table_name, df in tables_dict.items():
                if not df.empty:
                    df.to_parquet(config.GOLD_DIR / f"{table_name}.parquet")
                    logger.info(f"Saved {table_name} with {len(df)} records to parquet")
                else:
                    logger.warning(f"Skipped empty table: {table_name}")
        
        except Exception as e:
            logger.error(f"Error saving to parquet: {str(e)}")
    
    def process_gold_layer(self):
        """Main processing function for gold layer"""
        logger.info("Starting Gold Layer processing")
        
        # Load silver data
        projects_df, expenses_df, budgets_df = self.load_silver_data()
        
        if projects_df is None:
            logger.error("Failed to load silver data")
            return
        
        # Create dimension tables
        dim_projects = self.create_dim_projects(projects_df)
        dim_time = self.create_dim_time()
        dim_categories = self.create_dim_categories(expenses_df, budgets_df)
        
        # Create fact tables
        fact_expenses = self.create_fact_expenses(expenses_df, dim_projects, dim_time, dim_categories)
        fact_budgets = self.create_fact_budgets(budgets_df, dim_projects, dim_time, dim_categories)
        
        # Create summary tables
        summaries = self.create_summary_tables(fact_expenses, fact_budgets, dim_projects, dim_time, dim_categories)
        
        # Combine all tables
        all_tables = {
            'dim_projects': dim_projects,
            'dim_time': dim_time,
            'dim_categories': dim_categories,
            'fact_expenses': fact_expenses,
            'fact_budgets': fact_budgets,
            **summaries
        }
        
        # Save to database and parquet
        self.save_to_database(all_tables)
        self.save_to_parquet(all_tables)
        
        logger.info("Gold Layer processing completed")
        return all_tables

if __name__ == "__main__":
    processor = GoldLayerProcessor()
    processor.process_gold_layer()
