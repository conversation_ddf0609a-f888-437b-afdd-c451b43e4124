"""
Configuration settings for MSF Data Engineering Project
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Project paths
PROJECT_ROOT = Path(__file__).parent
DATA_DIR = PROJECT_ROOT / "OneDrive_1_7-4-2025"
OUTPUT_DIR = PROJECT_ROOT / "output"
BRONZE_DIR = OUTPUT_DIR / "bronze"
SILVER_DIR = OUTPUT_DIR / "silver"
GOLD_DIR = OUTPUT_DIR / "gold"

# Create directories if they don't exist
for dir_path in [OUTPUT_DIR, BRONZE_DIR, SILVER_DIR, GOLD_DIR]:
    dir_path.mkdir(exist_ok=True)

# Exchange Rate API Configuration
EXCHANGE_RATE_API_KEY = os.getenv("EXCHANGE_RATE_API_KEY", "your_api_key_here")
EXCHANGE_RATE_BASE_URL = "https://v6.exchangerate-api.com/v6"

# Database Configuration
DATABASE_URL = "sqlite:///msf_data_warehouse.db"

# Project Configuration
PROJECT_CODES = ["BE01", "BE55", "BF01", "BF02", "KE01", "KE02", "SN01", "SN02"]
BASE_CURRENCY = "EUR"
YEARS = [2023, 2024, 2025]

# Logging Configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}"
