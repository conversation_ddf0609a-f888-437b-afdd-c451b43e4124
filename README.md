# MSF Data Engineering Project

This project implements a complete data engineering solution for MSF (Médecins Sans Frontières) project data, featuring a medallion architecture (Bronze-Silver-Gold) for data processing and transformation.

## Project Structure

```
msf-augment/
├── OneDrive_1_7-4-2025/          # Source data files
│   ├── *.db                      # SQLite databases (BE01.db, BE55.db, etc.)
│   ├── *_budget.csv              # Budget CSV files
│   └── powerbi*.png              # Power BI report screenshots
├── src/                          # Source code
│   ├── bronze_layer.py           # Data extraction from raw sources
│   ├── silver_layer.py           # Data cleaning and transformation
│   ├── gold_layer.py             # Analytical data modeling
│   └── exchange_rate_service.py  # Currency conversion service
├── output/                       # Generated output files
│   ├── bronze/                   # Raw extracted data
│   ├── silver/                   # Cleaned and transformed data
│   └── gold/                     # Analytical data models
├── config.py                     # Configuration settings
├── main.py                       # Main pipeline execution
├── requirements.txt              # Python dependencies
└── README.md                     # This file
```

## Features

### Medallion Architecture
- **Bronze Layer**: Raw data extraction from SQLite databases and CSV files
- **Silver Layer**: Data cleaning, standardization, and currency conversion
- **Gold Layer**: Analytical data modeling with fact and dimension tables

### Data Processing
- Extracts data from 8 MSF projects (BE01, BE55, BF01, BF02, KE01, KE02, SN01, SN02)
- Handles both expense data (from SQLite) and budget data (from CSV)
- Implements currency conversion using historical exchange rates
- Creates consistent data schema across all projects

### Currency Conversion
- Uses exchangerate-api.com for historical exchange rates
- Converts all amounts to EUR using end-of-month rates
- Handles multiple source currencies (USD, EUR, etc.)

### Data Quality
- Data validation and cleaning
- Missing data handling
- Consistent formatting and standardization
- Error logging and monitoring

## Setup Instructions

### 1. Prerequisites
- Python 3.8 or higher
- Internet connection (for exchange rate API)

### 2. Installation
```bash
# Clone or download the project
cd msf-augment

# Install dependencies
pip install -r requirements.txt
```

### 3. Configuration
```bash
# Copy the environment template
cp .env.template .env

# Edit .env and add your exchange rate API key
# Get a free key from https://www.exchangerate-api.com/
```

### 4. Data Preparation
Ensure the `OneDrive_1_7-4-2025` directory contains all required files:
- SQLite databases: BE01.db, BE55.db, BF01.db, BF02.db, KE01.db, KE02.db, SN01.db, SN02.db
- Budget CSV files: BE01_budget.csv, BE55_budget.csv, etc.

### 5. Run the Pipeline
```bash
python main.py
```

## Output

The pipeline generates several outputs:

### Database
- `msf_data_warehouse.db`: SQLite database with all analytical tables

### Parquet Files
- Bronze layer: Raw extracted data
- Silver layer: Cleaned and transformed data  
- Gold layer: Analytical data models

### Tables Created
- `dim_projects`: Project dimension
- `dim_time`: Time dimension
- `dim_categories`: Department/Category dimension
- `fact_expenses`: Expense facts
- `fact_budgets`: Budget facts
- Summary tables for reporting

## Data Model

### Dimension Tables
- **dim_projects**: Project information (ID, name, country, currency, region)
- **dim_time**: Time hierarchy (year, month, quarter)
- **dim_categories**: Department and category combinations

### Fact Tables
- **fact_expenses**: Expense transactions with EUR conversion
- **fact_budgets**: Budget allocations in EUR

### Summary Tables
- **monthly_expenses_by_project**: Monthly expense totals by project
- **monthly_expenses_by_category**: Monthly expense totals by category
- **budget_vs_actual**: Budget variance analysis

## Architecture Decisions

### Medallion Architecture
- **Bronze**: Minimal transformation, preserves raw data integrity
- **Silver**: Business logic applied, data quality ensured
- **Gold**: Optimized for analytics and reporting

### Currency Conversion
- Uses end-of-month exchange rates for consistency
- Caches rates to minimize API calls
- Handles conversion errors gracefully

### Data Storage
- Parquet format for efficient storage and querying
- SQLite for easy data access and analysis
- Maintains data lineage throughout pipeline

## Scalability Considerations

### For More Projects
- Configuration-driven project list
- Parallel processing capabilities
- Automated schema detection
- Dynamic table creation

### Performance Optimization
- Batch processing for currency conversion
- Incremental data loading
- Partitioning by time periods
- Indexing for query performance

### Future AI Features
- Anomaly detection in expenses
- Budget forecasting models
- Automated data quality monitoring
- Natural language query interface

## Monitoring and Logging

- Comprehensive logging throughout pipeline
- Data quality metrics tracking
- Error handling and recovery
- Processing statistics and summaries

## Usage Examples

### Accessing Data
```python
import pandas as pd
import sqlite3

# Connect to the data warehouse
conn = sqlite3.connect('msf_data_warehouse.db')

# Query expense data
expenses = pd.read_sql("""
    SELECT p.project_name, t.year_month, c.department, 
           SUM(f.amount_eur) as total_expenses
    FROM fact_expenses f
    JOIN dim_projects p ON f.project_id = p.project_id
    JOIN dim_time t ON f.time_key = t.time_key
    JOIN dim_categories c ON f.category_key = c.category_id
    GROUP BY p.project_name, t.year_month, c.department
""", conn)
```

### Budget vs Actual Analysis
```python
# Load budget vs actual data
budget_actual = pd.read_sql("SELECT * FROM budget_vs_actual", conn)

# Calculate variance statistics
variance_summary = budget_actual.groupby('project_id').agg({
    'budget_eur': 'sum',
    'amount_eur': 'sum',
    'variance_eur': 'sum'
}).reset_index()
```

## Support

For questions or issues, please check the logs in `output/pipeline.log` for detailed error information.
