"""
Mock Exchange Rate Service for Testing
Provides mock exchange rates when API key is not available
"""
import pandas as pd
from datetime import datetime, date
from loguru import logger

class MockExchangeRateService:
    """Mock service for testing without real API key"""
    
    def __init__(self):
        # Mock exchange rates (approximate historical rates)
        self.mock_rates = {
            'USD': {'EUR': 0.85, 'USD': 1.0},
            'EUR': {'EUR': 1.0, 'USD': 1.18},
            'XOF': {'EUR': 0.0015, 'USD': 0.0018},  # West African CFA franc
            'KES': {'EUR': 0.0067, 'USD': 0.0079},  # Kenyan Shilling
        }
    
    def get_last_day_of_month(self, year, month):
        """Get the last day of a given month"""
        if month == 12:
            next_month = date(year + 1, 1, 1)
        else:
            next_month = date(year, month + 1, 1)
        last_day = next_month - pd.Timedelta(days=1)
        return last_day.strftime('%Y-%m-%d')
    
    def fetch_historical_rates(self, base_currency, target_date):
        """
        Mock historical exchange rates for a specific date
        
        Args:
            base_currency (str): Base currency code (e.g., 'USD')
            target_date (str): Date in YYYY-MM-DD format
            
        Returns:
            dict: Mock exchange rates with currency codes as keys
        """
        logger.info(f"Using mock exchange rates for {base_currency} on {target_date}")
        
        if base_currency in self.mock_rates:
            return self.mock_rates[base_currency]
        else:
            # Default rates if currency not found
            logger.warning(f"No mock rates for {base_currency}, using default")
            return {'EUR': 0.85, 'USD': 1.0}
    
    def convert_currency(self, amount, from_currency, to_currency, year, month):
        """
        Convert currency amount using mock historical rates
        
        Args:
            amount (float): Amount to convert
            from_currency (str): Source currency code
            to_currency (str): Target currency code
            year (int): Year
            month (int): Month
            
        Returns:
            float: Converted amount
        """
        if from_currency == to_currency:
            return amount
        
        # Get mock rates
        target_date = self.get_last_day_of_month(year, month)
        rates = self.fetch_historical_rates(from_currency, target_date)
        
        if rates and to_currency in rates:
            converted_amount = amount * rates[to_currency]
            logger.debug(f"Mock converted {amount} {from_currency} to {converted_amount:.2f} {to_currency}")
            return converted_amount
        else:
            logger.warning(f"Could not mock convert {from_currency} to {to_currency}")
            # Return original amount as fallback
            return amount
    
    def get_monthly_exchange_rates(self, currencies, years, months):
        """
        Get mock exchange rates for multiple currencies and months
        
        Args:
            currencies (list): List of currency codes
            years (list): List of years
            months (list): List of months (1-12)
            
        Returns:
            DataFrame: Mock exchange rates with columns [year, month, currency, rate_to_eur]
        """
        exchange_data = []
        
        for year in years:
            for month in months:
                target_date = self.get_last_day_of_month(year, month)
                
                for currency in currencies:
                    if currency == 'EUR':
                        # Base currency rate is always 1
                        exchange_data.append({
                            'year': year,
                            'month': f"{month:02d}",
                            'currency': currency,
                            'rate_to_eur': 1.0,
                            'date': target_date
                        })
                    else:
                        # Get mock rate from currency to EUR
                        rates = self.fetch_historical_rates(currency, target_date)
                        rate = rates.get('EUR', 0.85)  # Default rate
                        exchange_data.append({
                            'year': year,
                            'month': f"{month:02d}",
                            'currency': currency,
                            'rate_to_eur': rate,
                            'date': target_date
                        })
        
        return pd.DataFrame(exchange_data)
