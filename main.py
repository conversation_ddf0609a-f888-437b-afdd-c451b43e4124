"""
MSF Data Engineering Project - Main Execution Script
This script orchestrates the entire data pipeline from Bronze to Gold layers
"""
import sys
import os
from pathlib import Path
from loguru import logger
import pandas as pd
import config

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from bronze_layer import process_all_projects
from silver_layer import SilverLayerProcessor
from gold_layer import GoldLayerProcessor

def setup_logging():
    """Configure logging"""
    logger.remove()  # Remove default handler
    logger.add(
        sys.stdout,
        format=config.LOG_FORMAT,
        level=config.LOG_LEVEL,
        colorize=True
    )
    logger.add(
        config.OUTPUT_DIR / "pipeline.log",
        format=config.LOG_FORMAT,
        level=config.LOG_LEVEL,
        rotation="10 MB"
    )

def run_pipeline():
    """Run the complete data pipeline"""
    logger.info("=" * 60)
    logger.info("MSF Data Engineering Pipeline - Starting")
    logger.info("=" * 60)
    
    try:
        # Bronze Layer - Data Extraction
        logger.info("Step 1: Bronze Layer - Data Extraction")
        logger.info("-" * 40)
        projects_df, expenses_df, budgets_df = process_all_projects()
        
        if projects_df.empty and expenses_df.empty and budgets_df.empty:
            logger.error("No data extracted in Bronze layer. Stopping pipeline.")
            return False
        
        # Silver Layer - Data Transformation
        logger.info("Step 2: Silver Layer - Data Transformation")
        logger.info("-" * 40)
        silver_processor = SilverLayerProcessor()
        clean_projects, clean_expenses, clean_budgets = silver_processor.process_silver_layer()
        
        # Gold Layer - Data Modeling
        logger.info("Step 3: Gold Layer - Data Modeling")
        logger.info("-" * 40)
        gold_processor = GoldLayerProcessor()
        gold_tables = gold_processor.process_gold_layer()
        
        logger.info("=" * 60)
        logger.info("MSF Data Engineering Pipeline - Completed Successfully")
        logger.info("=" * 60)
        
        # Print summary statistics
        print_summary(projects_df, expenses_df, budgets_df, gold_tables)
        
        return True
        
    except Exception as e:
        logger.error(f"Pipeline failed with error: {str(e)}")
        logger.exception("Full error traceback:")
        return False

def print_summary(projects_df, expenses_df, budgets_df, gold_tables):
    """Print pipeline summary statistics"""
    logger.info("PIPELINE SUMMARY")
    logger.info("=" * 60)
    
    # Bronze layer summary
    logger.info("Bronze Layer:")
    logger.info(f"  - Projects: {len(projects_df)} records")
    logger.info(f"  - Expenses: {len(expenses_df)} records")
    logger.info(f"  - Budgets: {len(budgets_df)} records")
    
    # Gold layer summary
    if gold_tables:
        logger.info("Gold Layer:")
        for table_name, df in gold_tables.items():
            if not df.empty:
                logger.info(f"  - {table_name}: {len(df)} records")
    
    # Data quality checks
    logger.info("Data Quality:")
    if not expenses_df.empty:
        total_expenses_eur = gold_tables.get('fact_expenses', pd.DataFrame())
        if not total_expenses_eur.empty and 'amount_eur' in total_expenses_eur.columns:
            total_amount = total_expenses_eur['amount_eur'].sum()
            logger.info(f"  - Total expenses (EUR): {total_amount:,.2f}")
    
    if not budgets_df.empty:
        total_budgets_eur = gold_tables.get('fact_budgets', pd.DataFrame())
        if not total_budgets_eur.empty and 'budget_eur' in total_budgets_eur.columns:
            total_budget = total_budgets_eur['budget_eur'].sum()
            logger.info(f"  - Total budget (EUR): {total_budget:,.2f}")
    
    logger.info("=" * 60)

def main():
    """Main function"""
    # Setup logging
    setup_logging()
    
    # Check if data directory exists
    if not config.DATA_DIR.exists():
        logger.error(f"Data directory not found: {config.DATA_DIR}")
        logger.error("Please ensure the OneDrive_1_7-4-2025 directory exists with the required data files")
        return
    
    # Check for required files
    required_files = []
    for project_code in config.PROJECT_CODES:
        required_files.append(f"{project_code}.db")
        if project_code == "KE02":
            required_files.append("KEO2_budget.csv")
        else:
            required_files.append(f"{project_code}_budget.csv")
    
    missing_files = []
    for file_name in required_files:
        if not (config.DATA_DIR / file_name).exists():
            missing_files.append(file_name)
    
    if missing_files:
        logger.error(f"Missing required files: {missing_files}")
        logger.error("Please ensure all required data files are present")
        return
    
    # Run the pipeline
    success = run_pipeline()
    
    if success:
        logger.info("Pipeline completed successfully!")
        logger.info(f"Output files saved to: {config.OUTPUT_DIR}")
        logger.info(f"Database created at: {config.DATABASE_URL}")
    else:
        logger.error("Pipeline failed. Check logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
