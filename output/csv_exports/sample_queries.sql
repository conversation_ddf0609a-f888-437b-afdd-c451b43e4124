-- Sample SQL Queries for MSF Data Analysis

-- 1. Project Overview
SELECT project_name, country, region, project_currency
FROM dim_projects;

-- 2. Monthly Expense Trends
SELECT t.year_month, SUM(f.amount_eur) as total_expenses
FROM fact_expenses f
JOIN dim_time t ON f.time_key = t.time_key
GROUP BY t.year_month
ORDER BY t.year_month;

-- 3. Budget vs Actual by Project
SELECT p.project_name,
       SUM(b.budget_eur) as total_budget,
       SUM(b.amount_eur) as total_actual,
       SUM(b.variance_eur) as variance
FROM budget_vs_actual b
JOIN dim_projects p ON b.project_id = p.project_id
GROUP BY p.project_name
ORDER BY variance DESC;

-- 4. Top Expense Categories
SELECT c.department, c.category,
       COUNT(*) as transactions,
       SUM(f.amount_eur) as total_expenses
FROM fact_expenses f
JOIN dim_categories c ON f.category_key = c.category_id
GROUP BY c.department, c.category
ORDER BY total_expenses DESC;
