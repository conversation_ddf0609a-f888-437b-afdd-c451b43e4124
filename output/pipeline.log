2025-07-10 10:33:01 | INFO | __main__ | ============================================================
2025-07-10 10:33:01 | INFO | __main__ | MSF Data Engineering Pipeline - Starting
2025-07-10 10:33:01 | INFO | __main__ | ============================================================
2025-07-10 10:33:01 | INFO | __main__ | Step 1: Bronze Layer - Data Extraction
2025-07-10 10:33:01 | INFO | __main__ | ----------------------------------------
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted data from BE01.db: 1 projects, 432 expense records
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted budget data from BE01_budget.csv: 432 records
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted data from BE55.db: 1 projects, 432 expense records
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted budget data from BE55_budget.csv: 432 records
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted data from BF01.db: 1 projects, 432 expense records
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted budget data from BF01_budget.csv: 432 records
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted data from BF02.db: 1 projects, 432 expense records
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted budget data from BF02_budget.csv: 432 records
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted data from KE01.db: 1 projects, 432 expense records
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted budget data from KE01_budget.csv: 432 records
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted data from KE02.db: 1 projects, 432 expense records
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted budget data from KEO2_budget.csv: 432 records
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted data from SN01.db: 1 projects, 432 expense records
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted budget data from SN01_budget.csv: 432 records
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted data from SN02.db: 1 projects, 432 expense records
2025-07-10 10:33:01 | INFO | bronze_layer | Extracted budget data from SN02_budget.csv: 432 records
2025-07-10 10:33:01 | INFO | bronze_layer | Saved 8 project records to bronze layer
2025-07-10 10:33:01 | INFO | bronze_layer | Saved 3456 expense records to bronze layer
2025-07-10 10:33:01 | INFO | bronze_layer | Saved 3456 budget records to bronze layer
2025-07-10 10:33:01 | INFO | __main__ | Step 2: Silver Layer - Data Transformation
2025-07-10 10:33:01 | INFO | __main__ | ----------------------------------------
2025-07-10 10:33:01 | INFO | silver_layer | Using mock exchange rate service for testing
2025-07-10 10:33:01 | INFO | silver_layer | Starting Silver Layer processing
2025-07-10 10:33:01 | INFO | silver_layer | Loaded bronze data: 8 projects, 3456 expenses, 3456 budgets
2025-07-10 10:33:01 | INFO | silver_layer | Cleaned projects data: 8 records
2025-07-10 10:33:01 | WARNING | silver_layer | Removed 576 records with missing critical data
2025-07-10 10:33:01 | INFO | silver_layer | Cleaned expenses data: 2880 records
2025-07-10 10:33:01 | INFO | silver_layer | Cleaned budgets data: 3456 records
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-01-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-02-28
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-03-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-04-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-05-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-06-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-07-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-08-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-09-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-10-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-11-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-12-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-01-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-02-29
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-03-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-04-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-05-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-06-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-07-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-08-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-09-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-10-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-11-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-12-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-01-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-02-28
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-03-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-04-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-05-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-06-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-01-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-02-28
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-03-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-04-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-05-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-06-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-07-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-08-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-09-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-10-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-11-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-12-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-01-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-02-29
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-03-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-04-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-05-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-06-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-07-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-08-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-09-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-10-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-11-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-12-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-01-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-02-28
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-03-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-04-30
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-05-31
2025-07-10 10:33:01 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-06-30
2025-07-10 10:33:01 | INFO | silver_layer | Converted currencies for 2880 expense records
2025-07-10 10:33:01 | INFO | silver_layer | Silver Layer processing completed
2025-07-10 10:33:01 | INFO | __main__ | Step 3: Gold Layer - Data Modeling
2025-07-10 10:33:01 | INFO | __main__ | ----------------------------------------
2025-07-10 10:33:01 | INFO | gold_layer | Starting Gold Layer processing
2025-07-10 10:33:01 | INFO | gold_layer | Loaded silver data: 8 projects, 2880 expenses, 3456 budgets
2025-07-10 10:33:01 | INFO | gold_layer | Created dim_projects with 8 records
2025-07-10 10:33:01 | INFO | gold_layer | Created dim_time with 36 records
2025-07-10 10:33:01 | INFO | gold_layer | Created dim_categories with 12 records
2025-07-10 10:33:01 | INFO | gold_layer | Created fact_expenses with 2880 records
2025-07-10 10:33:01 | INFO | gold_layer | Created fact_budgets with 3456 records
2025-07-10 10:33:01 | INFO | gold_layer | Created 3 summary tables
2025-07-10 10:33:01 | INFO | gold_layer | Saved dim_projects with 8 records to database
2025-07-10 10:33:01 | INFO | gold_layer | Saved dim_time with 36 records to database
2025-07-10 10:33:01 | INFO | gold_layer | Saved dim_categories with 12 records to database
2025-07-10 10:33:01 | INFO | gold_layer | Saved fact_expenses with 2880 records to database
2025-07-10 10:33:01 | INFO | gold_layer | Saved fact_budgets with 3456 records to database
2025-07-10 10:33:01 | INFO | gold_layer | Saved monthly_expenses_by_project with 240 records to database
2025-07-10 10:33:01 | INFO | gold_layer | Saved monthly_expenses_by_category with 360 records to database
2025-07-10 10:33:01 | INFO | gold_layer | Saved budget_vs_actual with 3456 records to database
2025-07-10 10:33:01 | INFO | gold_layer | Saved dim_projects with 8 records to parquet
2025-07-10 10:33:01 | INFO | gold_layer | Saved dim_time with 36 records to parquet
2025-07-10 10:33:01 | INFO | gold_layer | Saved dim_categories with 12 records to parquet
2025-07-10 10:33:01 | INFO | gold_layer | Saved fact_expenses with 2880 records to parquet
2025-07-10 10:33:01 | INFO | gold_layer | Saved fact_budgets with 3456 records to parquet
2025-07-10 10:33:01 | INFO | gold_layer | Saved monthly_expenses_by_project with 240 records to parquet
2025-07-10 10:33:01 | INFO | gold_layer | Saved monthly_expenses_by_category with 360 records to parquet
2025-07-10 10:33:01 | INFO | gold_layer | Saved budget_vs_actual with 3456 records to parquet
2025-07-10 10:33:01 | INFO | gold_layer | Gold Layer processing completed
2025-07-10 10:33:01 | INFO | __main__ | ============================================================
2025-07-10 10:33:01 | INFO | __main__ | MSF Data Engineering Pipeline - Completed Successfully
2025-07-10 10:33:01 | INFO | __main__ | ============================================================
2025-07-10 10:33:01 | INFO | __main__ | PIPELINE SUMMARY
2025-07-10 10:33:01 | INFO | __main__ | ============================================================
2025-07-10 10:33:01 | INFO | __main__ | Bronze Layer:
2025-07-10 10:33:01 | INFO | __main__ |   - Projects: 8 records
2025-07-10 10:33:01 | INFO | __main__ |   - Expenses: 3456 records
2025-07-10 10:33:01 | INFO | __main__ |   - Budgets: 3456 records
2025-07-10 10:33:01 | INFO | __main__ | Gold Layer:
2025-07-10 10:33:01 | INFO | __main__ |   - dim_projects: 8 records
2025-07-10 10:33:01 | INFO | __main__ |   - dim_time: 36 records
2025-07-10 10:33:01 | INFO | __main__ |   - dim_categories: 12 records
2025-07-10 10:33:01 | INFO | __main__ |   - fact_expenses: 2880 records
2025-07-10 10:33:01 | INFO | __main__ |   - fact_budgets: 3456 records
2025-07-10 10:33:01 | INFO | __main__ |   - monthly_expenses_by_project: 240 records
2025-07-10 10:33:01 | INFO | __main__ |   - monthly_expenses_by_category: 360 records
2025-07-10 10:33:01 | INFO | __main__ |   - budget_vs_actual: 3456 records
2025-07-10 10:33:01 | INFO | __main__ | Data Quality:
2025-07-10 10:33:01 | ERROR | __main__ | Pipeline failed with error: name 'pd' is not defined
2025-07-10 10:33:01 | ERROR | __main__ | Full error traceback:
Traceback (most recent call last):

  File "/Users/<USER>/test/code-signal/msf-augment/main.py", line 152, in <module>
    main()
    └ <function main at 0x123a107c0>

  File "/Users/<USER>/test/code-signal/msf-augment/main.py", line 141, in main
    success = run_pipeline()
              └ <function run_pipeline at 0x117bbd3a0>

> File "/Users/<USER>/test/code-signal/msf-augment/main.py", line 67, in run_pipeline
    print_summary(projects_df, expenses_df, budgets_df, gold_tables)
    │             │            │            │           └ {'dim_projects':   project_id               project_name  ... project_currency  region
    │             │            │            │             0       BE01            Brussels Coord...
    │             │            │            └        id  year  month  ... version project_code      source_file
    │             │            │              0       1  2023      1  ...      v1         BE01  BE01_budg...
    │             │            └        id  year month  ... project_code source_file  amount_eur
    │             │              0       1  2023    01  ...         BE01     BE01.db         N...
    │             └      id                       name       country currency source_file
    │               0  BE01            Brussels Coordo       Belgium      U...
    └ <function print_summary at 0x117bbd440>

  File "/Users/<USER>/test/code-signal/msf-augment/main.py", line 97, in print_summary
    total_expenses_eur = gold_tables.get('fact_expenses', pd.DataFrame())
                         │           └ <method 'get' of 'dict' objects>
                         └ {'dim_projects':   project_id               project_name  ... project_currency  region
                           0       BE01            Brussels Coord...

NameError: name 'pd' is not defined
2025-07-10 10:33:01 | ERROR | __main__ | Pipeline failed. Check logs for details.
2025-07-10 10:34:40 | INFO | __main__ | ============================================================
2025-07-10 10:34:40 | INFO | __main__ | MSF Data Engineering Pipeline - Starting
2025-07-10 10:34:40 | INFO | __main__ | ============================================================
2025-07-10 10:34:40 | INFO | __main__ | Step 1: Bronze Layer - Data Extraction
2025-07-10 10:34:40 | INFO | __main__ | ----------------------------------------
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted data from BE01.db: 1 projects, 432 expense records
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted budget data from BE01_budget.csv: 432 records
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted data from BE55.db: 1 projects, 432 expense records
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted budget data from BE55_budget.csv: 432 records
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted data from BF01.db: 1 projects, 432 expense records
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted budget data from BF01_budget.csv: 432 records
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted data from BF02.db: 1 projects, 432 expense records
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted budget data from BF02_budget.csv: 432 records
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted data from KE01.db: 1 projects, 432 expense records
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted budget data from KE01_budget.csv: 432 records
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted data from KE02.db: 1 projects, 432 expense records
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted budget data from KEO2_budget.csv: 432 records
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted data from SN01.db: 1 projects, 432 expense records
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted budget data from SN01_budget.csv: 432 records
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted data from SN02.db: 1 projects, 432 expense records
2025-07-10 10:34:40 | INFO | bronze_layer | Extracted budget data from SN02_budget.csv: 432 records
2025-07-10 10:34:40 | INFO | bronze_layer | Saved 8 project records to bronze layer
2025-07-10 10:34:40 | INFO | bronze_layer | Saved 3456 expense records to bronze layer
2025-07-10 10:34:40 | INFO | bronze_layer | Saved 3456 budget records to bronze layer
2025-07-10 10:34:40 | INFO | __main__ | Step 2: Silver Layer - Data Transformation
2025-07-10 10:34:40 | INFO | __main__ | ----------------------------------------
2025-07-10 10:34:40 | INFO | silver_layer | Using mock exchange rate service for testing
2025-07-10 10:34:40 | INFO | silver_layer | Starting Silver Layer processing
2025-07-10 10:34:40 | INFO | silver_layer | Loaded bronze data: 8 projects, 3456 expenses, 3456 budgets
2025-07-10 10:34:40 | INFO | silver_layer | Cleaned projects data: 8 records
2025-07-10 10:34:40 | WARNING | silver_layer | Removed 576 records with missing critical data
2025-07-10 10:34:40 | INFO | silver_layer | Cleaned expenses data: 2880 records
2025-07-10 10:34:40 | INFO | silver_layer | Cleaned budgets data: 3456 records
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-01-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-02-28
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-03-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-04-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-05-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-06-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-07-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-08-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-09-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-10-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-11-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-12-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-01-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-02-29
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-03-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-04-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-05-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-06-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-07-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-08-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-09-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-10-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-11-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-12-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-01-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-02-28
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-03-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-04-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-05-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-06-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-01-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-02-28
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-03-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-04-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-05-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-06-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-07-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-08-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-09-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-10-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-11-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-12-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-01-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-02-29
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-03-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-04-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-05-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-06-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-07-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-08-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-09-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-10-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-11-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-12-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-01-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-02-28
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-03-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-04-30
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-05-31
2025-07-10 10:34:40 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-06-30
2025-07-10 10:34:40 | INFO | silver_layer | Converted currencies for 2880 expense records
2025-07-10 10:34:40 | INFO | silver_layer | Silver Layer processing completed
2025-07-10 10:34:40 | INFO | __main__ | Step 3: Gold Layer - Data Modeling
2025-07-10 10:34:40 | INFO | __main__ | ----------------------------------------
2025-07-10 10:34:40 | INFO | gold_layer | Starting Gold Layer processing
2025-07-10 10:34:40 | INFO | gold_layer | Loaded silver data: 8 projects, 2880 expenses, 3456 budgets
2025-07-10 10:34:40 | INFO | gold_layer | Created dim_projects with 8 records
2025-07-10 10:34:40 | INFO | gold_layer | Created dim_time with 36 records
2025-07-10 10:34:40 | INFO | gold_layer | Created dim_categories with 12 records
2025-07-10 10:34:40 | INFO | gold_layer | Created fact_expenses with 2880 records
2025-07-10 10:34:40 | INFO | gold_layer | Created fact_budgets with 3456 records
2025-07-10 10:34:40 | INFO | gold_layer | Created 3 summary tables
2025-07-10 10:34:40 | INFO | gold_layer | Saved dim_projects with 8 records to database
2025-07-10 10:34:40 | INFO | gold_layer | Saved dim_time with 36 records to database
2025-07-10 10:34:40 | INFO | gold_layer | Saved dim_categories with 12 records to database
2025-07-10 10:34:40 | INFO | gold_layer | Saved fact_expenses with 2880 records to database
2025-07-10 10:34:40 | INFO | gold_layer | Saved fact_budgets with 3456 records to database
2025-07-10 10:34:40 | INFO | gold_layer | Saved monthly_expenses_by_project with 240 records to database
2025-07-10 10:34:40 | INFO | gold_layer | Saved monthly_expenses_by_category with 360 records to database
2025-07-10 10:34:40 | INFO | gold_layer | Saved budget_vs_actual with 3456 records to database
2025-07-10 10:34:40 | INFO | gold_layer | Saved dim_projects with 8 records to parquet
2025-07-10 10:34:40 | INFO | gold_layer | Saved dim_time with 36 records to parquet
2025-07-10 10:34:40 | INFO | gold_layer | Saved dim_categories with 12 records to parquet
2025-07-10 10:34:40 | INFO | gold_layer | Saved fact_expenses with 2880 records to parquet
2025-07-10 10:34:40 | INFO | gold_layer | Saved fact_budgets with 3456 records to parquet
2025-07-10 10:34:40 | INFO | gold_layer | Saved monthly_expenses_by_project with 240 records to parquet
2025-07-10 10:34:40 | INFO | gold_layer | Saved monthly_expenses_by_category with 360 records to parquet
2025-07-10 10:34:40 | INFO | gold_layer | Saved budget_vs_actual with 3456 records to parquet
2025-07-10 10:34:40 | INFO | gold_layer | Gold Layer processing completed
2025-07-10 10:34:40 | INFO | __main__ | ============================================================
2025-07-10 10:34:40 | INFO | __main__ | MSF Data Engineering Pipeline - Completed Successfully
2025-07-10 10:34:40 | INFO | __main__ | ============================================================
2025-07-10 10:34:40 | INFO | __main__ | PIPELINE SUMMARY
2025-07-10 10:34:40 | INFO | __main__ | ============================================================
2025-07-10 10:34:40 | INFO | __main__ | Bronze Layer:
2025-07-10 10:34:40 | INFO | __main__ |   - Projects: 8 records
2025-07-10 10:34:40 | INFO | __main__ |   - Expenses: 3456 records
2025-07-10 10:34:40 | INFO | __main__ |   - Budgets: 3456 records
2025-07-10 10:34:40 | INFO | __main__ | Gold Layer:
2025-07-10 10:34:40 | INFO | __main__ |   - dim_projects: 8 records
2025-07-10 10:34:40 | INFO | __main__ |   - dim_time: 36 records
2025-07-10 10:34:40 | INFO | __main__ |   - dim_categories: 12 records
2025-07-10 10:34:40 | INFO | __main__ |   - fact_expenses: 2880 records
2025-07-10 10:34:40 | INFO | __main__ |   - fact_budgets: 3456 records
2025-07-10 10:34:40 | INFO | __main__ |   - monthly_expenses_by_project: 240 records
2025-07-10 10:34:40 | INFO | __main__ |   - monthly_expenses_by_category: 360 records
2025-07-10 10:34:40 | INFO | __main__ |   - budget_vs_actual: 3456 records
2025-07-10 10:34:40 | INFO | __main__ | Data Quality:
2025-07-10 10:34:40 | INFO | __main__ |   - Total expenses (EUR): 7,381,891.28
2025-07-10 10:34:40 | INFO | __main__ |   - Total budget (EUR): 10,411,489.91
2025-07-10 10:34:40 | INFO | __main__ | ============================================================
2025-07-10 10:34:40 | INFO | __main__ | Pipeline completed successfully!
2025-07-10 10:34:40 | INFO | __main__ | Output files saved to: /Users/<USER>/test/code-signal/msf-augment/output
2025-07-10 10:34:40 | INFO | __main__ | Database created at: sqlite:///msf_data_warehouse.db
2025-07-10 11:12:14 | INFO | __main__ | ============================================================
2025-07-10 11:12:14 | INFO | __main__ | MSF Data Engineering Pipeline - Starting
2025-07-10 11:12:14 | INFO | __main__ | ============================================================
2025-07-10 11:12:14 | INFO | __main__ | Step 1: Bronze Layer - Data Extraction
2025-07-10 11:12:14 | INFO | __main__ | ----------------------------------------
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted data from BE01.db: 1 projects, 432 expense records
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted budget data from BE01_budget.csv: 432 records
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted data from BE55.db: 1 projects, 432 expense records
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted budget data from BE55_budget.csv: 432 records
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted data from BF01.db: 1 projects, 432 expense records
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted budget data from BF01_budget.csv: 432 records
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted data from BF02.db: 1 projects, 432 expense records
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted budget data from BF02_budget.csv: 432 records
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted data from KE01.db: 1 projects, 432 expense records
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted budget data from KE01_budget.csv: 432 records
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted data from KE02.db: 1 projects, 432 expense records
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted budget data from KEO2_budget.csv: 432 records
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted data from SN01.db: 1 projects, 432 expense records
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted budget data from SN01_budget.csv: 432 records
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted data from SN02.db: 1 projects, 432 expense records
2025-07-10 11:12:14 | INFO | bronze_layer | Extracted budget data from SN02_budget.csv: 432 records
2025-07-10 11:12:14 | INFO | bronze_layer | Saved 8 project records to bronze layer
2025-07-10 11:12:14 | INFO | bronze_layer | Saved 3456 expense records to bronze layer
2025-07-10 11:12:14 | INFO | bronze_layer | Saved 3456 budget records to bronze layer
2025-07-10 11:12:14 | INFO | __main__ | Step 2: Silver Layer - Data Transformation
2025-07-10 11:12:14 | INFO | __main__ | ----------------------------------------
2025-07-10 11:12:14 | INFO | silver_layer | Using mock exchange rate service for testing
2025-07-10 11:12:14 | INFO | silver_layer | Starting Silver Layer processing
2025-07-10 11:12:14 | INFO | silver_layer | Loaded bronze data: 8 projects, 3456 expenses, 3456 budgets
2025-07-10 11:12:14 | INFO | silver_layer | Corrected geographical data: SN01 'Dakkar Coordo' moved from Kenya to Senegal
2025-07-10 11:12:14 | INFO | silver_layer | Applied 1 data quality corrections
2025-07-10 11:12:14 | INFO | silver_layer | Cleaned projects data: 8 records
2025-07-10 11:12:14 | WARNING | silver_layer | Removed 576 records with missing critical data
2025-07-10 11:12:14 | INFO | silver_layer | Cleaned expenses data: 2880 records
2025-07-10 11:12:14 | INFO | silver_layer | Cleaned budgets data: 3456 records
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-01-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-02-28
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-03-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-04-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-05-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-06-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-07-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-08-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-09-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-10-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-11-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2023-12-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-01-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-02-29
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-03-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-04-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-05-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-06-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-07-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-08-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-09-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-10-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-11-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2024-12-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-01-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-02-28
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-03-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-04-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-05-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for XOF on 2025-06-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-01-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-02-28
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-03-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-04-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-05-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-06-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-07-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-08-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-09-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-10-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-11-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2023-12-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-01-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-02-29
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-03-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-04-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-05-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-06-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-07-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-08-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-09-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-10-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-11-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2024-12-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-01-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-02-28
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-03-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-04-30
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-05-31
2025-07-10 11:12:14 | INFO | mock_exchange_service | Using mock exchange rates for KES on 2025-06-30
2025-07-10 11:12:14 | INFO | silver_layer | Converted currencies for 2880 expense records
2025-07-10 11:12:14 | INFO | silver_layer | Silver Layer processing completed
2025-07-10 11:12:14 | INFO | __main__ | Step 3: Gold Layer - Data Modeling
2025-07-10 11:12:14 | INFO | __main__ | ----------------------------------------
2025-07-10 11:12:14 | INFO | gold_layer | Starting Gold Layer processing
2025-07-10 11:12:14 | INFO | gold_layer | Loaded silver data: 8 projects, 2880 expenses, 3456 budgets
2025-07-10 11:12:14 | INFO | gold_layer | Created dim_projects with 8 records
2025-07-10 11:12:14 | INFO | gold_layer | Created dim_time with 36 records
2025-07-10 11:12:14 | INFO | gold_layer | Created dim_categories with 12 records
2025-07-10 11:12:14 | INFO | gold_layer | Created fact_expenses with 2880 records
2025-07-10 11:12:14 | INFO | gold_layer | Created fact_budgets with 3456 records
2025-07-10 11:12:14 | INFO | gold_layer | Created 3 summary tables
2025-07-10 11:12:14 | INFO | gold_layer | Saved dim_projects with 8 records to database
2025-07-10 11:12:14 | INFO | gold_layer | Saved dim_time with 36 records to database
2025-07-10 11:12:14 | INFO | gold_layer | Saved dim_categories with 12 records to database
2025-07-10 11:12:14 | INFO | gold_layer | Saved fact_expenses with 2880 records to database
2025-07-10 11:12:14 | INFO | gold_layer | Saved fact_budgets with 3456 records to database
2025-07-10 11:12:14 | INFO | gold_layer | Saved monthly_expenses_by_project with 240 records to database
2025-07-10 11:12:14 | INFO | gold_layer | Saved monthly_expenses_by_category with 360 records to database
2025-07-10 11:12:14 | INFO | gold_layer | Saved budget_vs_actual with 3456 records to database
2025-07-10 11:12:14 | INFO | gold_layer | Saved dim_projects with 8 records to parquet
2025-07-10 11:12:14 | INFO | gold_layer | Saved dim_time with 36 records to parquet
2025-07-10 11:12:14 | INFO | gold_layer | Saved dim_categories with 12 records to parquet
2025-07-10 11:12:14 | INFO | gold_layer | Saved fact_expenses with 2880 records to parquet
2025-07-10 11:12:14 | INFO | gold_layer | Saved fact_budgets with 3456 records to parquet
2025-07-10 11:12:14 | INFO | gold_layer | Saved monthly_expenses_by_project with 240 records to parquet
2025-07-10 11:12:14 | INFO | gold_layer | Saved monthly_expenses_by_category with 360 records to parquet
2025-07-10 11:12:14 | INFO | gold_layer | Saved budget_vs_actual with 3456 records to parquet
2025-07-10 11:12:14 | INFO | gold_layer | Gold Layer processing completed
2025-07-10 11:12:14 | INFO | __main__ | ============================================================
2025-07-10 11:12:14 | INFO | __main__ | MSF Data Engineering Pipeline - Completed Successfully
2025-07-10 11:12:14 | INFO | __main__ | ============================================================
2025-07-10 11:12:14 | INFO | __main__ | PIPELINE SUMMARY
2025-07-10 11:12:14 | INFO | __main__ | ============================================================
2025-07-10 11:12:14 | INFO | __main__ | Bronze Layer:
2025-07-10 11:12:14 | INFO | __main__ |   - Projects: 8 records
2025-07-10 11:12:14 | INFO | __main__ |   - Expenses: 3456 records
2025-07-10 11:12:14 | INFO | __main__ |   - Budgets: 3456 records
2025-07-10 11:12:14 | INFO | __main__ | Gold Layer:
2025-07-10 11:12:14 | INFO | __main__ |   - dim_projects: 8 records
2025-07-10 11:12:14 | INFO | __main__ |   - dim_time: 36 records
2025-07-10 11:12:14 | INFO | __main__ |   - dim_categories: 12 records
2025-07-10 11:12:14 | INFO | __main__ |   - fact_expenses: 2880 records
2025-07-10 11:12:14 | INFO | __main__ |   - fact_budgets: 3456 records
2025-07-10 11:12:14 | INFO | __main__ |   - monthly_expenses_by_project: 240 records
2025-07-10 11:12:14 | INFO | __main__ |   - monthly_expenses_by_category: 360 records
2025-07-10 11:12:14 | INFO | __main__ |   - budget_vs_actual: 3456 records
2025-07-10 11:12:14 | INFO | __main__ | Data Quality:
2025-07-10 11:12:14 | INFO | __main__ |   - Total expenses (EUR): 7,381,891.28
2025-07-10 11:12:14 | INFO | __main__ |   - Total budget (EUR): 10,411,489.91
2025-07-10 11:12:14 | INFO | __main__ | ============================================================
2025-07-10 11:12:14 | INFO | __main__ | Pipeline completed successfully!
2025-07-10 11:12:14 | INFO | __main__ | Output files saved to: /Users/<USER>/test/code-signal/msf-augment/output
2025-07-10 11:12:14 | INFO | __main__ | Database created at: sqlite:///msf_data_warehouse.db
